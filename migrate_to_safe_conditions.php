<?php

/**
 * Script pour migrer les conditions user_role vers safe_user_role
 */

// Connexion directe à la base de données
$host = '127.0.0.1';
$dbname = 'juillet2025';
$username = 'root';
$password = 'password';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== Migration vers les conditions sûres ===\n\n";
    
    // 1. Activer le module context_fix d'abord
    echo "1. Activation du module context_fix...\n";
    
    // Vérifier si le module est déjà activé
    $stmt = $pdo->prepare("SELECT data FROM config WHERE name = 'core.extension'");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result) {
        $data = unserialize($result['data']);
        
        if (!isset($data['module']['context_fix'])) {
            $data['module']['context_fix'] = 0;
            
            // Trier les modules par ordre alphabétique
            ksort($data['module']);
            
            $stmt = $pdo->prepare("UPDATE config SET data = ? WHERE name = 'core.extension'");
            $stmt->execute([serialize($data)]);
            
            echo "  - Module context_fix activé\n";
        } else {
            echo "  - Module context_fix déjà activé\n";
        }
    }
    
    // 2. Migrer les blocs avec conditions user_role
    echo "\n2. Migration des blocs avec conditions user_role...\n";
    
    $stmt = $pdo->prepare("SELECT name, data FROM config WHERE name LIKE 'block.block.%' AND data LIKE '%user_role%'");
    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $migrated_count = 0;
    
    foreach ($results as $row) {
        $data = unserialize($row['data']);
        $modified = false;
        
        if (isset($data['visibility']['user_role'])) {
            echo "  - Migration du bloc: {$row['name']}\n";
            
            // Créer une sauvegarde
            $backup_name = $row['name'] . '_backup_before_migration_' . date('Y_m_d_H_i_s');
            $stmt = $pdo->prepare("INSERT INTO config (name, data, collection) VALUES (?, ?, '')");
            $stmt->execute([$backup_name, serialize($data)]);
            
            // Remplacer user_role par safe_user_role
            $data['visibility']['user_role']['id'] = 'safe_user_role';
            $modified = true;
            
            if ($modified) {
                $stmt = $pdo->prepare("UPDATE config SET data = ? WHERE name = ?");
                $stmt->execute([serialize($data), $row['name']]);
                
                echo "    ✓ Migré vers safe_user_role\n";
                echo "    ✓ Sauvegarde créée: $backup_name\n";
                $migrated_count++;
            }
        }
    }
    
    echo "\n  Total de blocs migrés: $migrated_count\n";
    
    // 3. Vider le cache
    echo "\n3. Nettoyage du cache...\n";
    
    // Supprimer les fichiers de cache
    $cache_dirs = [
        'sites/default/files/php/twig',
        'sites/default/files/css',
        'sites/default/files/js'
    ];
    
    foreach ($cache_dirs as $dir) {
        if (is_dir($dir)) {
            $files = glob($dir . '/*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }
            echo "  - Cache vidé: $dir\n";
        }
    }
    
    // Vider le cache de la base de données
    $cache_tables = ['cache_bootstrap', 'cache_config', 'cache_container', 'cache_data', 'cache_default', 'cache_discovery', 'cache_dynamic_page_cache', 'cache_entity', 'cache_menu', 'cache_page', 'cache_render', 'cache_toolbar'];
    
    foreach ($cache_tables as $table) {
        try {
            $stmt = $pdo->prepare("TRUNCATE TABLE $table");
            $stmt->execute();
            echo "  - Table de cache vidée: $table\n";
        } catch (PDOException $e) {
            // Table might not exist, continue
        }
    }
    
    echo "\n=== Migration terminée avec succès ===\n";
    echo "\nInstructions post-migration :\n";
    echo "1. Visitez votre site pour vérifier que l'erreur a disparu\n";
    echo "2. Si tout fonctionne, vous pouvez supprimer les sauvegardes plus tard\n";
    echo "3. Pour annuler la migration, restaurez les sauvegardes avec :\n";
    echo "   UPDATE config SET data = (SELECT data FROM config WHERE name LIKE '%_backup_before_migration_%' AND name LIKE '%nom_du_bloc%' ORDER BY name DESC LIMIT 1) WHERE name = 'nom_du_bloc';\n\n";
    
} catch (PDOException $e) {
    echo "Erreur de connexion à la base de données: " . $e->getMessage() . "\n";
}

echo "=== Fin de la migration ===\n";
