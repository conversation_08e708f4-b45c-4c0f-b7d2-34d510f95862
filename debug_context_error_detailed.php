<?php

/**
 * Script pour obtenir une trace détaillée de l'erreur de contexte
 */

use Drupal\Core\DrupalKernel;
use Symfony\Component\HttpFoundation\Request;

$autoloader = require_once 'autoload.php';

$kernel = new DrupalKernel('prod', $autoloader);
$request = Request::createFromGlobals();
$kernel->setSitePath('sites/default');
$kernel->boot();
$kernel->preHandle($request);

// Activer le mode debug pour capturer les erreurs
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Fonction pour capturer les erreurs de contexte
set_error_handler(function($severity, $message, $file, $line) {
    if (strpos($message, 'entity:user') !== false || strpos($message, 'ContextException') !== false) {
        echo "=== ERREUR DE CONTEXTE DÉTECTÉE ===\n";
        echo "Message: $message\n";
        echo "Fichier: $file\n";
        echo "Ligne: $line\n";
        echo "Trace:\n";
        debug_print_backtrace();
        echo "\n=== FIN DE LA TRACE ===\n\n";
    }
    return false; // Laisser le gestionnaire d'erreur par défaut continuer
});

// Fonction pour capturer les exceptions
set_exception_handler(function($exception) {
    if (strpos($exception->getMessage(), 'entity:user') !== false) {
        echo "=== EXCEPTION DE CONTEXTE DÉTECTÉE ===\n";
        echo "Message: " . $exception->getMessage() . "\n";
        echo "Fichier: " . $exception->getFile() . "\n";
        echo "Ligne: " . $exception->getLine() . "\n";
        echo "Trace:\n";
        echo $exception->getTraceAsString() . "\n";
        echo "\n=== FIN DE LA TRACE ===\n\n";
    }
});

echo "=== Démarrage du diagnostic détaillé ===\n\n";

try {
    // Simuler une requête pour déclencher l'erreur
    $response = $kernel->handle($request);
    echo "Réponse reçue avec code: " . $response->getStatusCode() . "\n";
    
} catch (Exception $e) {
    echo "Exception capturée: " . $e->getMessage() . "\n";
    echo "Fichier: " . $e->getFile() . "\n";
    echo "Ligne: " . $e->getLine() . "\n";
    echo "Trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== Fin du diagnostic ===\n";
