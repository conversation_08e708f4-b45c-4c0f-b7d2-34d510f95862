<?php

/**
 * Script pour vérifier les configurations en base de données
 */

// Connexion directe à la base de données
$host = '127.0.0.1';
$dbname = 'juillet2025';
$username = 'root';
$password = 'password';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "=== Vérification des configurations en base de données ===\n\n";

    // 1. Vérifier les configurations qui contiennent 'entity:user'
    echo "1. Configurations contenant 'entity:user' :\n";
    $stmt = $pdo->prepare("SELECT name, data FROM config WHERE data LIKE '%entity:user%'");
    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($results as $row) {
        echo "  - Configuration: {$row['name']}\n";
        $data = unserialize($row['data']);
        if ($data) {
            echo "    Contenu (extrait): " . substr(print_r($data, true), 0, 200) . "...\n";
        }
        echo "\n";
    }

    // 2. Vérifier les configurations de blocs avec visibilité
    echo "\n2. Blocs avec conditions de visibilité :\n";
    $stmt = $pdo->prepare("SELECT name, data FROM config WHERE name LIKE 'block.block.%' AND data LIKE '%visibility%'");
    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($results as $row) {
        $data = unserialize($row['data']);
        if ($data && isset($data['visibility']) && !empty($data['visibility'])) {
            echo "  - Bloc: {$row['name']}\n";
            foreach ($data['visibility'] as $condition_id => $condition_config) {
                echo "    Condition: {$condition_id}\n";
                if (isset($condition_config['context_mapping'])) {
                    foreach ($condition_config['context_mapping'] as $context_name => $context_mapping) {
                        echo "      Contexte: {$context_name} -> {$context_mapping}\n";
                        if (strpos($context_mapping, 'entity:user') !== false) {
                            echo "        *** PROBLÈME: Contexte entity:user requis ***\n";
                        }
                    }
                }
            }
            echo "\n";
        }
    }

    // 3. Vérifier les configurations Rules/ECA
    echo "\n3. Configurations Rules/ECA :\n";
    $stmt = $pdo->prepare("SELECT name, data FROM config WHERE (name LIKE '%rules%' OR name LIKE '%eca%') AND data LIKE '%user_email_verification%'");
    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($results)) {
        echo "  Aucune configuration Rules/ECA trouvée avec user_email_verification\n";
    } else {
        foreach ($results as $row) {
            echo "  - Configuration: {$row['name']}\n";
            $data = unserialize($row['data']);
            if ($data) {
                echo "    Contenu: " . print_r($data, true) . "\n";
            }
            echo "\n";
        }
    }

    // 4. Vérifier les configurations de conditions
    echo "\n4. Configurations de conditions :\n";
    $stmt = $pdo->prepare("SELECT name, data FROM config WHERE data LIKE '%user_email_verification_user_email_verified%' OR data LIKE '%user_email_verification_period_exceeded%' OR data LIKE '%user_email_verification_extended_period_exceeded%'");
    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($results)) {
        echo "  Aucune configuration trouvée utilisant les conditions user_email_verification\n";
    } else {
        foreach ($results as $row) {
            echo "  - Configuration: {$row['name']}\n";
            $data = unserialize($row['data']);
            if ($data) {
                echo "    Contenu: " . print_r($data, true) . "\n";
            }
            echo "\n";
        }
    }
} catch (PDOException $e) {
    echo "Erreur de connexion à la base de données: " . $e->getMessage() . "\n";
}

echo "\n=== Fin de la vérification ===\n";
