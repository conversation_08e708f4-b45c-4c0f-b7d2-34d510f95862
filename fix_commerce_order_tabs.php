<?php

/**
 * <PERSON>ript pour corriger l'onglet Address book qui cause l'erreur de contexte
 */

// Connexion directe à la base de données
$host = '127.0.0.1';
$dbname = 'juillet2025';
$username = 'root';
$password = 'password';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== Correction de l'onglet Address book ===\n\n";
    
    // Le problème vient du fichier commerce_order.links.task.yml
    $task_file = 'modules/contrib/commerce/modules/order/commerce_order.links.task.yml';
    
    if (file_exists($task_file)) {
        echo "Fichier trouvé: $task_file\n";
        
        // Créer une sauvegarde
        $backup_file = $task_file . '.backup.' . date('Y_m_d_H_i_s');
        copy($task_file, $backup_file);
        echo "Sauvegarde créée: $backup_file\n";
        
        // Lire le contenu
        $content = file_get_contents($task_file);
        
        // Supprimer l'onglet problématique
        $lines = explode("\n", $content);
        $new_lines = [];
        $skip_lines = false;
        
        foreach ($lines as $line) {
            if (strpos($line, 'commerce_order.address_book.overview:') !== false) {
                echo "Suppression de l'onglet Address book...\n";
                $skip_lines = true;
                continue;
            }
            
            // Arrêter de supprimer quand on arrive à une nouvelle section
            if ($skip_lines && !empty(trim($line)) && !preg_match('/^  /', $line)) {
                $skip_lines = false;
            }
            
            if (!$skip_lines) {
                $new_lines[] = $line;
            }
        }
        
        // Écrire le nouveau contenu
        file_put_contents($task_file, implode("\n", $new_lines));
        echo "✓ Onglet Address book supprimé\n";
        
    } else {
        echo "Fichier non trouvé: $task_file\n";
    }
    
    // Vider le cache
    echo "\nVidage du cache...\n";
    $cache_tables = ['cache_bootstrap', 'cache_config', 'cache_container', 'cache_data', 'cache_default', 'cache_discovery', 'cache_dynamic_page_cache', 'cache_entity', 'cache_menu', 'cache_page', 'cache_render', 'cache_toolbar'];
    
    foreach ($cache_tables as $table) {
        try {
            $stmt = $pdo->prepare("DELETE FROM $table");
            $stmt->execute();
        } catch (PDOException $e) {
            // Continue if table doesn't exist
        }
    }
    
    // Supprimer les fichiers de cache
    $cache_dirs = ['sites/default/files/php'];
    foreach ($cache_dirs as $dir) {
        if (is_dir($dir)) {
            $iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
                RecursiveIteratorIterator::CHILD_FIRST
            );
            
            foreach ($iterator as $file) {
                if ($file->isFile()) {
                    unlink($file->getRealPath());
                }
            }
        }
    }
    
    echo "✓ Cache vidé\n\n";
    
    echo "=== Correction terminée ===\n";
    echo "L'onglet 'Address book' a été supprimé des onglets locaux.\n";
    echo "Testez maintenant votre site. L'erreur de contexte devrait avoir disparu.\n";
    echo "Pour restaurer l'onglet :\n";
    echo "cp $backup_file $task_file\n\n";
    
    echo "Note: L'Address book reste accessible directement via /user/[uid]/address-book\n";
    
} catch (PDOException $e) {
    echo "Erreur: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "Erreur: " . $e->getMessage() . "\n";
}
