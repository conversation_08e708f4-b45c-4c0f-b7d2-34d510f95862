<?php

/**
 * Script pour réactiver les blocs et tester
 */

// Connexion directe à la base de données
$host = '127.0.0.1';
$dbname = 'juillet2025';
$username = 'root';
$password = 'password';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== Réactivation des blocs ===\n\n";
    
    // Blocs à réactiver
    $blocks_to_reactivate = [
        'block.block.bootstrap5_menuadmin',
        'block.block.bootstrap5_menuseo',
        'block.block.bootstrap5_resetcache'
    ];
    
    foreach ($blocks_to_reactivate as $block_name) {
        echo "Réactivation du bloc: $block_name\n";
        
        // Trouver la sauvegarde la plus récente
        $stmt = $pdo->prepare("SELECT name, data FROM config WHERE name LIKE ? ORDER BY name DESC LIMIT 1");
        $stmt->execute([$block_name . '_backup_active_state_%']);
        $backup = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($backup) {
            $backup_data = unserialize($backup['data']);
            
            // Restaurer la configuration
            $stmt = $pdo->prepare("UPDATE config SET data = ? WHERE name = ?");
            $stmt->execute([serialize($backup_data), $block_name]);
            
            echo "  ✓ Bloc réactivé depuis: {$backup['name']}\n";
        } else {
            echo "  - Aucune sauvegarde trouvée\n";
        }
        echo "\n";
    }
    
    // Vider le cache
    echo "Vidage du cache...\n";
    $cache_tables = ['cache_bootstrap', 'cache_config', 'cache_container', 'cache_data', 'cache_default', 'cache_discovery', 'cache_dynamic_page_cache', 'cache_entity', 'cache_menu', 'cache_page', 'cache_render', 'cache_toolbar'];
    
    foreach ($cache_tables as $table) {
        try {
            $stmt = $pdo->prepare("DELETE FROM $table");
            $stmt->execute();
        } catch (PDOException $e) {
            // Continue if table doesn't exist
        }
    }
    echo "  ✓ Cache vidé\n\n";
    
    echo "=== Test du site ===\n";
    echo "Les blocs ont été réactivés. Testez maintenant le site.\n\n";
    
} catch (PDOException $e) {
    echo "Erreur: " . $e->getMessage() . "\n";
}
