#!/bin/bash

echo "=== Analyse des logs d'erreur en temps réel ==="
echo ""

# Vider les logs actuels pour avoir une vue claire
sudo truncate -s 0 /var/log/apache2/error.log

echo "Logs vidés. Faites une requête sur le site maintenant..."
echo "Appuyez sur Ctrl+C pour arrêter l'analyse"
echo ""

# Surveiller les logs en temps réel
tail -f /var/log/apache2/error.log | while read line; do
    if [[ "$line" == *"ContextException"* ]] || [[ "$line" == *"entity:user"* ]]; then
        echo "=== ERREUR DÉTECTÉE ==="
        echo "$line"
        echo ""
        
        # Extraire plus d'informations si possible
        if [[ "$line" == *"line"* ]]; then
            echo "Ligne extraite: $(echo "$line" | grep -o 'line [0-9]*')"
        fi
        
        if [[ "$line" == *"core/"* ]]; then
            echo "Fichier core impliqué: $(echo "$line" | grep -o 'core/[^[:space:]]*')"
        fi
        
        if [[ "$line" == *"modules/"* ]]; then
            echo "Module impliqué: $(echo "$line" | grep -o 'modules/[^[:space:]]*')"
        fi
        
        echo "========================"
        echo ""
    fi
done
