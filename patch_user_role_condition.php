<?php

/**
 * Script pour patcher temporairement le plugin UserRole
 */

echo "=== Patch temporaire du plugin UserRole ===\n\n";

$user_role_file = 'core/modules/user/src/Plugin/Condition/UserRole.php';

if (!file_exists($user_role_file)) {
    echo "Erreur: Fichier UserRole.php non trouvé\n";
    exit(1);
}

// Lire le contenu actuel
$content = file_get_contents($user_role_file);

// Créer une sauvegarde
$backup_file = $user_role_file . '.backup.' . date('Y_m_d_H_i_s');
file_put_contents($backup_file, $content);
echo "Sauvegarde créée: $backup_file\n";

// Rechercher la méthode evaluate et la remplacer
$old_evaluate = '  public function evaluate() {
    if (empty($this->configuration[\'roles\']) && !$this->isNegated()) {
      return TRUE;
    }
    $user = $this->getContextValue(\'user\');
    return (bool) array_intersect($this->configuration[\'roles\'], $user->getRoles());
  }';

$new_evaluate = '  public function evaluate() {
    if (empty($this->configuration[\'roles\']) && !$this->isNegated()) {
      return TRUE;
    }
    try {
      $user = $this->getContextValue(\'user\');
      // Vérifier si l\'utilisateur est valide
      if (!$user || !$user->id()) {
        return FALSE;
      }
      return (bool) array_intersect($this->configuration[\'roles\'], $user->getRoles());
    } catch (\Drupal\Component\Plugin\Exception\ContextException $e) {
      // Si le contexte n\'est pas disponible, considérer comme utilisateur anonyme
      return FALSE;
    }
  }';

// Remplacer le contenu
$new_content = str_replace($old_evaluate, $new_evaluate, $content);

if ($new_content === $content) {
    echo "Attention: Aucune modification détectée. Le code pourrait avoir déjà été modifié.\n";
} else {
    // Écrire le nouveau contenu
    file_put_contents($user_role_file, $new_content);
    echo "Patch appliqué avec succès\n";
}

// Vider le cache
echo "\nVidage du cache...\n";

// Supprimer les fichiers de cache PHP
$cache_dirs = [
    'sites/default/files/php'
];

foreach ($cache_dirs as $dir) {
    if (is_dir($dir)) {
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::CHILD_FIRST
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                unlink($file->getRealPath());
            }
        }
        echo "Cache PHP vidé: $dir\n";
    }
}

// Vider le cache de la base de données
try {
    $pdo = new PDO("mysql:host=127.0.0.1;dbname=juillet2025", 'root', 'password');
    $cache_tables = ['cache_bootstrap', 'cache_config', 'cache_container', 'cache_data', 'cache_default', 'cache_discovery', 'cache_dynamic_page_cache', 'cache_entity', 'cache_menu', 'cache_page', 'cache_render', 'cache_toolbar'];
    
    foreach ($cache_tables as $table) {
        try {
            $stmt = $pdo->prepare("DELETE FROM $table");
            $stmt->execute();
        } catch (PDOException $e) {
            // Continue if table doesn't exist
        }
    }
    echo "Cache de base de données vidé\n";
} catch (PDOException $e) {
    echo "Erreur lors du vidage du cache DB: " . $e->getMessage() . "\n";
}

echo "\n=== Patch terminé ===\n";
echo "Testez maintenant votre site.\n";
echo "Pour annuler le patch, restaurez le fichier de sauvegarde :\n";
echo "cp $backup_file $user_role_file\n\n";
