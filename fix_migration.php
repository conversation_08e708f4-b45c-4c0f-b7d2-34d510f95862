<?php

/**
 * Script pour corriger la migration des conditions user_role
 */

// Connexion directe à la base de données
$host = '127.0.0.1';
$dbname = 'juillet2025';
$username = 'root';
$password = 'password';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== Correction de la migration ===\n\n";
    
    // Blocs problématiques
    $problematic_blocks = [
        'block.block.bootstrap5_menuadmin',
        'block.block.bootstrap5_menuseo',
        'block.block.bootstrap5_resetcache'
    ];
    
    foreach ($problematic_blocks as $block_name) {
        echo "Correction du bloc: $block_name\n";
        
        $stmt = $pdo->prepare("SELECT data FROM config WHERE name = ?");
        $stmt->execute([$block_name]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result) {
            $data = unserialize($result['data']);
            
            if (isset($data['visibility']['user_role'])) {
                echo "  - Remplacement de user_role par safe_user_role\n";
                
                // Sauvegarder la configuration de la condition
                $user_role_config = $data['visibility']['user_role'];
                
                // Supprimer l'ancienne condition
                unset($data['visibility']['user_role']);
                
                // Ajouter la nouvelle condition avec le bon ID
                $user_role_config['id'] = 'safe_user_role';
                $data['visibility']['safe_user_role'] = $user_role_config;
                
                // Mettre à jour
                $stmt = $pdo->prepare("UPDATE config SET data = ? WHERE name = ?");
                $stmt->execute([serialize($data), $block_name]);
                
                echo "  ✓ Condition migrée avec succès\n";
            } else {
                echo "  - Aucune condition user_role trouvée\n";
            }
        }
        echo "\n";
    }
    
    // Vider complètement le cache
    echo "Vidage complet du cache...\n";
    
    // Supprimer tous les caches de base de données
    $cache_tables = [
        'cache_bootstrap', 'cache_config', 'cache_container', 'cache_data', 
        'cache_default', 'cache_discovery', 'cache_dynamic_page_cache', 
        'cache_entity', 'cache_menu', 'cache_page', 'cache_render', 
        'cache_toolbar', 'cache_form'
    ];
    
    foreach ($cache_tables as $table) {
        try {
            $stmt = $pdo->prepare("DELETE FROM $table");
            $stmt->execute();
            echo "  - Cache vidé: $table\n";
        } catch (PDOException $e) {
            // Table might not exist
        }
    }
    
    // Supprimer les fichiers de cache
    $cache_dirs = [
        'sites/default/files/php',
        'sites/default/files/css',
        'sites/default/files/js'
    ];
    
    foreach ($cache_dirs as $dir) {
        if (is_dir($dir)) {
            $iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
                RecursiveIteratorIterator::CHILD_FIRST
            );
            
            foreach ($iterator as $file) {
                if ($file->isFile()) {
                    unlink($file->getRealPath());
                }
            }
            echo "  - Fichiers de cache supprimés: $dir\n";
        }
    }
    
    echo "\n=== Correction terminée ===\n";
    echo "Testez maintenant votre site pour vérifier que l'erreur a disparu.\n\n";
    
} catch (PDOException $e) {
    echo "Erreur: " . $e->getMessage() . "\n";
}
