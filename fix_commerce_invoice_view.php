<?php

/**
 * Script pour corriger la vue commerce_user_invoices qui cause l'erreur de contexte
 */

// Connexion directe à la base de données
$host = '127.0.0.1';
$dbname = 'juillet2025';
$username = 'root';
$password = 'password';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== Correction de la vue commerce_user_invoices ===\n\n";
    
    $view_name = 'views.view.commerce_user_invoices';
    
    // Récupérer la configuration actuelle
    $stmt = $pdo->prepare("SELECT data FROM config WHERE name = ?");
    $stmt->execute([$view_name]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result) {
        $data = unserialize($result['data']);
        
        // Créer une sauvegarde
        $backup_name = $view_name . '_backup_' . date('Y_m_d_H_i_s');
        $stmt = $pdo->prepare("INSERT INTO config (name, data, collection) VALUES (?, ?, '')");
        $stmt->execute([$backup_name, serialize($data)]);
        echo "Sauvegarde créée: $backup_name\n";
        
        $modified = false;
        
        // Option 1: Supprimer l'onglet de menu
        if (isset($data['display']['invoice_page']['display_options']['menu'])) {
            echo "Suppression de l'onglet de menu...\n";
            unset($data['display']['invoice_page']['display_options']['menu']);
            $modified = true;
        }
        
        // Option 2: Désactiver complètement la vue (alternative)
        // if ($data['status']) {
        //     echo "Désactivation de la vue...\n";
        //     $data['status'] = false;
        //     $modified = true;
        // }
        
        if ($modified) {
            $stmt = $pdo->prepare("UPDATE config SET data = ? WHERE name = ?");
            $stmt->execute([serialize($data), $view_name]);
            echo "✓ Vue corrigée avec succès\n";
        } else {
            echo "Aucune modification nécessaire\n";
        }
        
    } else {
        echo "Vue non trouvée dans la base de données\n";
    }
    
    // Vider le cache
    echo "\nVidage du cache...\n";
    $cache_tables = ['cache_bootstrap', 'cache_config', 'cache_container', 'cache_data', 'cache_default', 'cache_discovery', 'cache_dynamic_page_cache', 'cache_entity', 'cache_menu', 'cache_page', 'cache_render', 'cache_toolbar'];
    
    foreach ($cache_tables as $table) {
        try {
            $stmt = $pdo->prepare("DELETE FROM $table");
            $stmt->execute();
        } catch (PDOException $e) {
            // Continue if table doesn't exist
        }
    }
    
    // Supprimer les fichiers de cache
    $cache_dirs = ['sites/default/files/php'];
    foreach ($cache_dirs as $dir) {
        if (is_dir($dir)) {
            $iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
                RecursiveIteratorIterator::CHILD_FIRST
            );
            
            foreach ($iterator as $file) {
                if ($file->isFile()) {
                    unlink($file->getRealPath());
                }
            }
        }
    }
    
    echo "✓ Cache vidé\n\n";
    
    echo "=== Correction terminée ===\n";
    echo "Testez maintenant votre site. L'erreur de contexte devrait avoir disparu.\n";
    echo "La vue est toujours accessible directement via /user/[uid]/invoices\n";
    echo "Pour restaurer la configuration originale :\n";
    echo "UPDATE config SET data = (SELECT data FROM config WHERE name = '$backup_name') WHERE name = '$view_name';\n\n";
    
} catch (PDOException $e) {
    echo "Erreur: " . $e->getMessage() . "\n";
}
