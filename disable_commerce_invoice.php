<?php

/**
 * Script pour désactiver temporairement le module commerce_invoice
 */

// Connexion directe à la base de données
$host = '127.0.0.1';
$dbname = 'juillet2025';
$username = 'root';
$password = 'password';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== Désactivation temporaire du module commerce_invoice ===\n\n";
    
    // Récupérer la configuration des modules
    $stmt = $pdo->prepare("SELECT data FROM config WHERE name = 'core.extension'");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result) {
        $data = unserialize($result['data']);
        
        // Créer une sauvegarde
        $backup_name = 'core.extension_backup_' . date('Y_m_d_H_i_s');
        $stmt = $pdo->prepare("INSERT INTO config (name, data, collection) VALUES (?, ?, '')");
        $stmt->execute([$backup_name, serialize($data)]);
        echo "Sauvegarde créée: $backup_name\n";
        
        if (isset($data['module']['commerce_invoice'])) {
            echo "Désactivation du module commerce_invoice...\n";
            unset($data['module']['commerce_invoice']);
            
            // Mettre à jour la configuration
            $stmt = $pdo->prepare("UPDATE config SET data = ? WHERE name = 'core.extension'");
            $stmt->execute([serialize($data)]);
            
            echo "✓ Module commerce_invoice désactivé\n";
        } else {
            echo "Module commerce_invoice déjà désactivé\n";
        }
        
        // Vider le cache
        echo "\nVidage du cache...\n";
        $cache_tables = ['cache_bootstrap', 'cache_config', 'cache_container', 'cache_data', 'cache_default', 'cache_discovery', 'cache_dynamic_page_cache', 'cache_entity', 'cache_menu', 'cache_page', 'cache_render', 'cache_toolbar'];
        
        foreach ($cache_tables as $table) {
            try {
                $stmt = $pdo->prepare("DELETE FROM $table");
                $stmt->execute();
            } catch (PDOException $e) {
                // Continue if table doesn't exist
            }
        }
        
        // Supprimer les fichiers de cache
        $cache_dirs = ['sites/default/files/php'];
        foreach ($cache_dirs as $dir) {
            if (is_dir($dir)) {
                $iterator = new RecursiveIteratorIterator(
                    new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
                    RecursiveIteratorIterator::CHILD_FIRST
                );
                
                foreach ($iterator as $file) {
                    if ($file->isFile()) {
                        unlink($file->getRealPath());
                    }
                }
            }
        }
        
        echo "✓ Cache vidé\n\n";
        
        echo "=== Test du site ===\n";
        echo "Le module commerce_invoice a été désactivé temporairement.\n";
        echo "Testez maintenant le site pour voir si l'erreur disparaît.\n";
        echo "Pour réactiver le module :\n";
        echo "UPDATE config SET data = (SELECT data FROM config WHERE name = '$backup_name') WHERE name = 'core.extension';\n\n";
        
    } else {
        echo "Configuration core.extension non trouvée\n";
    }
    
} catch (PDOException $e) {
    echo "Erreur: " . $e->getMessage() . "\n";
}
