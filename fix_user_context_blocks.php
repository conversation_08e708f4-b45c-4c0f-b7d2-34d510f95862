<?php

/**
 * Script pour corriger les blocs qui causent l'erreur de contexte 'entity:user'
 */

// Connexion directe à la base de données
$host = '127.0.0.1';
$dbname = 'juillet2025';
$username = 'root';
$password = 'password';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== Correction des blocs avec problème de contexte utilisateur ===\n\n";
    
    // Blocs problématiques identifiés
    $problematic_blocks = [
        'block.block.bootstrap5_menuadmin',
        'block.block.bootstrap5_menuseo',
        'block.block.bootstrap5_resetcache'
    ];
    
    foreach ($problematic_blocks as $block_name) {
        echo "Traitement du bloc: $block_name\n";
        
        // Récupérer la configuration actuelle
        $stmt = $pdo->prepare("SELECT data FROM config WHERE name = ?");
        $stmt->execute([$block_name]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result) {
            $data = unserialize($result['data']);
            
            // Ajouter une condition pour que le bloc ne s'affiche que pour les utilisateurs connectés
            if (isset($data['visibility']['user_role'])) {
                echo "  - Ajout d'une condition d'utilisateur connecté\n";
                
                // Ajouter une condition pour les utilisateurs connectés
                $data['visibility']['user_is_logged_in'] = [
                    'id' => 'user_is_logged_in',
                    'negate' => false,
                    'context_mapping' => []
                ];
                
                // Mettre à jour la configuration
                $stmt = $pdo->prepare("UPDATE config SET data = ? WHERE name = ?");
                $stmt->execute([serialize($data), $block_name]);
                
                echo "  - Configuration mise à jour avec succès\n";
            } else {
                echo "  - Aucune condition user_role trouvée, pas de modification nécessaire\n";
            }
        } else {
            echo "  - Bloc non trouvé dans la base de données\n";
        }
        echo "\n";
    }
    
    echo "=== Alternative: Désactivation temporaire des blocs problématiques ===\n\n";
    
    // Alternative: désactiver temporairement les blocs problématiques
    foreach ($problematic_blocks as $block_name) {
        echo "Désactivation du bloc: $block_name\n";
        
        $stmt = $pdo->prepare("SELECT data FROM config WHERE name = ?");
        $stmt->execute([$block_name]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result) {
            $data = unserialize($result['data']);
            
            // Sauvegarder l'état actuel
            $backup_name = $block_name . '_backup_' . date('Y_m_d_H_i_s');
            $stmt = $pdo->prepare("INSERT INTO config (name, data, collection) VALUES (?, ?, '')");
            $stmt->execute([$backup_name, serialize($data)]);
            echo "  - Sauvegarde créée: $backup_name\n";
            
            // Désactiver le bloc
            $data['status'] = false;
            
            $stmt = $pdo->prepare("UPDATE config SET data = ? WHERE name = ?");
            $stmt->execute([serialize($data), $block_name]);
            
            echo "  - Bloc désactivé\n";
        }
        echo "\n";
    }
    
    echo "=== Vider le cache ===\n";
    echo "Exécutez les commandes suivantes pour vider le cache :\n";
    echo "- rm -rf sites/default/files/php/twig/*\n";
    echo "- Ou visitez /admin/config/development/performance et cliquez sur 'Clear all caches'\n\n";
    
    echo "=== Instructions pour réactiver les blocs ===\n";
    echo "Pour réactiver un bloc, exécutez :\n";
    echo "UPDATE config SET data = (SELECT data FROM config WHERE name LIKE '%_backup_%' AND name LIKE '%nom_du_bloc%' ORDER BY name DESC LIMIT 1) WHERE name = 'nom_du_bloc';\n\n";
    
} catch (PDOException $e) {
    echo "Erreur de connexion à la base de données: " . $e->getMessage() . "\n";
}

echo "=== Fin de la correction ===\n";
