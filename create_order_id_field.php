<?php

/**
 * Script pour créer le champ field_order_id sur le type de contenu devis
 */

use Drupal\Core\DrupalKernel;
use Symfony\Component\HttpFoundation\Request;

$autoloader = require_once 'autoload.php';

$kernel = new DrupalKernel('prod', $autoloader);
$request = Request::createFromGlobals();
$kernel->setSitePath('sites/default');
$kernel->boot();
$kernel->preHandle($request);

echo "=== Création du champ field_order_id ===\n\n";

try {
    // Vérifier si le champ existe déjà
    $field_storage = \Drupal\field\Entity\FieldStorageConfig::loadByName('node', 'field_order_id');
    
    if (!$field_storage) {
        echo "Création du field storage pour field_order_id...\n";
        
        $field_storage = \Drupal\field\Entity\FieldStorageConfig::create([
            'field_name' => 'field_order_id',
            'entity_type' => 'node',
            'type' => 'integer',
            'cardinality' => 1,
            'settings' => [
                'unsigned' => TRUE,
                'size' => 'normal',
            ],
        ]);
        $field_storage->save();
        echo "✓ Field storage créé\n";
    } else {
        echo "Field storage field_order_id existe déjà\n";
    }
    
    // Vérifier si le champ est attaché au type de contenu devis
    $field_config = \Drupal\field\Entity\FieldConfig::loadByName('node', 'devis', 'field_order_id');
    
    if (!$field_config) {
        echo "Création du field config pour le type devis...\n";
        
        $field_config = \Drupal\field\Entity\FieldConfig::create([
            'field_storage' => $field_storage,
            'bundle' => 'devis',
            'label' => 'ID de commande',
            'description' => 'ID de la commande Commerce associée à ce devis',
            'required' => FALSE,
            'settings' => [
                'min' => 1,
            ],
        ]);
        $field_config->save();
        echo "✓ Field config créé pour le type devis\n";
    } else {
        echo "Field config field_order_id existe déjà pour le type devis\n";
    }
    
    // Configurer l'affichage du champ (le masquer par défaut)
    $entity_display = \Drupal::entityTypeManager()
        ->getStorage('entity_view_display')
        ->load('node.devis.default');
    
    if ($entity_display) {
        $entity_display->removeComponent('field_order_id');
        $entity_display->save();
        echo "✓ Champ masqué dans l'affichage par défaut\n";
    }
    
    // Configurer le formulaire (le masquer aussi)
    $form_display = \Drupal::entityTypeManager()
        ->getStorage('entity_form_display')
        ->load('node.devis.default');
    
    if ($form_display) {
        $form_display->removeComponent('field_order_id');
        $form_display->save();
        echo "✓ Champ masqué dans le formulaire\n";
    }
    
    // Vider le cache
    echo "\nVidage du cache...\n";
    \Drupal::service('cache.bootstrap')->deleteAll();
    \Drupal::service('cache.config')->deleteAll();
    \Drupal::service('cache.default')->deleteAll();
    \Drupal::service('cache.entity')->deleteAll();
    echo "✓ Cache vidé\n";
    
    echo "\n=== Champ field_order_id créé avec succès ===\n";
    echo "Le champ est maintenant disponible pour stocker l'ID des commandes.\n";
    echo "Il est masqué dans l'interface utilisateur mais accessible par le code.\n\n";
    
} catch (Exception $e) {
    echo "Erreur: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}

echo "=== Fin de la création du champ ===\n";
