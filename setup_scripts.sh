#!/bin/bash

# Script pour configurer les permissions et préparer les scripts
# Usage: ./setup_scripts.sh

echo "=== Configuration des scripts de messages ==="

# Rendre les scripts exécutables
chmod +x delete_message_system.sh
chmod +x create_message_system.sh
chmod +x test_message_system.sh

echo "✓ Permissions définies"

# Vérifier la connexion à la base de données
echo "Vérification de la connexion à la base de données..."

DB_NAME="drupal"
DB_USER="drupal"
DB_PASS="drupal"
DB_HOST="localhost"

# Test de connexion
mysql -h"$DB_HOST" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "SELECT 1;" > /dev/null 2>&1

if [ $? -eq 0 ]; then
    echo "✓ Connexion à la base de données réussie"
else
    echo "✗ Erreur de connexion à la base de données"
    echo "Veuillez vérifier les paramètres de connexion dans les scripts"
    exit 1
fi

# Vérifier que Drush est disponible
if command -v drush &> /dev/null; then
    echo "✓ Drush disponible"
    drush status | grep "Drupal version"
else
    echo "⚠ Drush non trouvé - certaines fonctionnalités seront limitées"
fi

echo ""
echo "=== Scripts prêts à utiliser ==="
echo ""
echo "Usage:"
echo "1. Pour supprimer le système existant:"
echo "   ./delete_message_system.sh"
echo ""
echo "2. Pour créer le système de messages:"
echo "   ./create_message_system.sh"
echo ""
echo "3. Pour tester le système:"
echo "   ./test_message_system.sh"
echo ""
echo "Note: Modifiez les paramètres de base de données dans chaque script si nécessaire"
