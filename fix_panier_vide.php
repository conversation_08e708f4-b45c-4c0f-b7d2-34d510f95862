<?php

/**
 * Script pour corriger le problème de panier vide
 */

// Connexion directe à la base de données
$host = '127.0.0.1';
$dbname = 'renovation1';
$username = 'root';
$password = 'password';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== Correction du problème de panier vide ===\n\n";
    
    // 1. Vider le cache
    echo "1. Vidage du cache...\n";
    $cache_tables = ['cache_bootstrap', 'cache_config', 'cache_container', 'cache_data', 'cache_default', 'cache_discovery', 'cache_dynamic_page_cache', 'cache_entity', 'cache_menu', 'cache_page', 'cache_render', 'cache_toolbar'];
    
    foreach ($cache_tables as $table) {
        try {
            $stmt = $pdo->prepare("DELETE FROM $table");
            $stmt->execute();
        } catch (PDOException $e) {
            // Continue if table doesn't exist
        }
    }
    echo "✓ Cache vidé\n\n";
    
    // 2. Vérifier les commandes en état draft
    echo "2. Vérification des commandes en état draft...\n";
    $stmt = $pdo->prepare("
        SELECT co.order_id, co.mail, co.state, co.total_price__number as total,
               coi.title as item_title, coi.quantity
        FROM commerce_order co 
        LEFT JOIN commerce_order_item coi ON co.order_id = coi.order_id
        WHERE co.state = 'draft' 
        ORDER BY co.order_id DESC 
        LIMIT 10
    ");
    $stmt->execute();
    $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($orders)) {
        echo "Aucune commande en état draft trouvée.\n";
    } else {
        echo "Commandes en état draft trouvées :\n";
        foreach ($orders as $order) {
            echo "  - Commande #{$order['order_id']} - {$order['mail']} - {$order['total']}€";
            if ($order['item_title']) {
                echo " - Item: {$order['item_title']} (qty: {$order['quantity']})";
            } else {
                echo " - ⚠️  AUCUN ITEM (panier vide)";
            }
            echo "\n";
        }
    }
    echo "\n";
    
    // 3. Vérifier les devis avec field_order_id
    echo "3. Vérification des devis avec commandes associées...\n";
    $stmt = $pdo->prepare("
        SELECT n.nid, n.title, nfoi.field_order_id_value as order_id,
               co.state as order_state, co.total_price__number as total
        FROM node_field_data n
        LEFT JOIN node__field_order_id nfoi ON n.nid = nfoi.entity_id
        LEFT JOIN commerce_order co ON nfoi.field_order_id_value = co.order_id
        WHERE n.type = 'devis' AND nfoi.field_order_id_value IS NOT NULL
        ORDER BY n.nid DESC
        LIMIT 5
    ");
    $stmt->execute();
    $devis_orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($devis_orders)) {
        echo "Aucun devis avec commande associée trouvé.\n";
    } else {
        echo "Devis avec commandes associées :\n";
        foreach ($devis_orders as $devis) {
            echo "  - Devis #{$devis['nid']} '{$devis['title']}' -> Commande #{$devis['order_id']}";
            if ($devis['order_state']) {
                echo " (état: {$devis['order_state']}, total: {$devis['total']}€)";
            } else {
                echo " (⚠️  commande introuvable)";
            }
            echo "\n";
        }
    }
    echo "\n";
    
    // 4. Instructions pour tester
    echo "=== Corrections apportées ===\n\n";
    
    echo "✅ Problèmes corrigés :\n";
    echo "1. La fonction _client_demandes_create_commerce_order ne crée plus de panier en conflit\n";
    echo "2. Le PaiementController réutilise les commandes existantes au lieu d'en créer de nouvelles\n";
    echo "3. Les commandes sont correctement associées au panier de l'utilisateur\n\n";
    
    echo "🔧 Fichiers modifiés :\n";
    echo "- modules/custom/client_demandes/client_demandes.module (lignes 290-303)\n";
    echo "- modules/custom/client_demandes/src/Controller/PaiementController.php (lignes 27-96)\n\n";
    
    echo "📝 Test à effectuer :\n";
    echo "1. Connectez-vous en tant que client\n";
    echo "2. Allez sur un devis accepté (ex: /node/48)\n";
    echo "3. Cliquez sur 'Procéder au paiement'\n";
    echo "4. Vérifiez que le panier contient bien l'article\n";
    echo "5. Procédez au checkout\n\n";
    
    echo "🐛 Si le problème persiste :\n";
    echo "1. Vérifiez les logs : tail -f /var/log/apache2/error.log\n";
    echo "2. Vérifiez que le devis a bien un field_order_id\n";
    echo "3. Vérifiez que la commande existe et a des items\n\n";
    
} catch (PDOException $e) {
    echo "Erreur: " . $e->getMessage() . "\n";
}

echo "=== Correction terminée ===\n";
