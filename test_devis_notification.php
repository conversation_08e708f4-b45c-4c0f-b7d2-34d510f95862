<?php

/**
 * Script pour tester les nouvelles fonctionnalités de notification de devis
 */

// Connexion directe à la base de données
$host = '127.0.0.1';
$dbname = 'juillet2025';
$username = 'root';
$password = 'password';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== Test des nouvelles fonctionnalités de devis ===\n\n";
    
    // 1. Vider le cache pour activer les nouvelles fonctionnalités
    echo "1. Vidage du cache...\n";
    $cache_tables = ['cache_bootstrap', 'cache_config', 'cache_container', 'cache_data', 'cache_default', 'cache_discovery', 'cache_dynamic_page_cache', 'cache_entity', 'cache_menu', 'cache_page', 'cache_render', 'cache_toolbar'];
    
    foreach ($cache_tables as $table) {
        try {
            $stmt = $pdo->prepare("DELETE FROM $table");
            $stmt->execute();
        } catch (PDOException $e) {
            // Continue if table doesn't exist
        }
    }
    
    // Supprimer les fichiers de cache
    $cache_dirs = ['sites/default/files/php'];
    foreach ($cache_dirs as $dir) {
        if (is_dir($dir)) {
            $iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
                RecursiveIteratorIterator::CHILD_FIRST
            );
            
            foreach ($iterator as $file) {
                if ($file->isFile()) {
                    unlink($file->getRealPath());
                }
            }
        }
    }
    
    echo "✓ Cache vidé\n\n";
    
    // 2. Vérifier les commandes existantes en état draft
    echo "2. Vérification des commandes en état draft...\n";
    $stmt = $pdo->prepare("
        SELECT co.order_id, co.mail, co.state, cs.name as store_name, co.total_price__number as total
        FROM commerce_order co 
        LEFT JOIN commerce_store cs ON co.store_id = cs.store_id 
        WHERE co.state = 'draft' 
        ORDER BY co.order_id DESC 
        LIMIT 5
    ");
    $stmt->execute();
    $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($orders)) {
        echo "Aucune commande en état draft trouvée.\n";
    } else {
        echo "Commandes en état draft trouvées :\n";
        foreach ($orders as $order) {
            echo "  - Commande #{$order['order_id']} - {$order['mail']} - {$order['store_name']} - {$order['total']}€\n";
        }
    }
    echo "\n";
    
    // 3. Vérifier les utilisateurs clients
    echo "3. Vérification des utilisateurs clients...\n";
    $stmt = $pdo->prepare("
        SELECT u.uid, u.name, u.mail 
        FROM users_field_data u 
        JOIN user__roles ur ON u.uid = ur.entity_id 
        WHERE ur.roles_target_id = 'client' 
        LIMIT 3
    ");
    $stmt->execute();
    $clients = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($clients)) {
        echo "Aucun utilisateur avec le rôle 'client' trouvé.\n";
    } else {
        echo "Utilisateurs clients trouvés :\n";
        foreach ($clients as $client) {
            echo "  - {$client['name']} ({$client['mail']})\n";
        }
    }
    echo "\n";
    
    // 4. Instructions pour tester
    echo "=== Instructions pour tester les nouvelles fonctionnalités ===\n\n";
    
    echo "📧 Test de notification email :\n";
    echo "1. Connectez-vous en tant que prestataire\n";
    echo "2. Créez un nouveau devis pour un client\n";
    echo "3. Sauvegardez le devis en état 'Draft'\n";
    echo "4. Le client devrait recevoir un email automatiquement\n";
    echo "5. Vérifiez les logs : tail -f /var/log/apache2/error.log | grep client_demandes\n\n";
    
    echo "🔍 Test d'affichage ID et Store :\n";
    echo "1. Allez dans Structure > Vues\n";
    echo "2. Éditez la vue qui affiche les commandes/devis\n";
    echo "3. Ajoutez les champs :\n";
    echo "   - 'Order ID Display' (affiche #123)\n";
    echo "   - 'Store Name Display' (affiche le nom du store)\n";
    echo "4. Sauvegardez et vérifiez l'affichage\n\n";
    
    echo "📝 Fonctionnalités ajoutées :\n";
    echo "✓ Notification email automatique lors de la création de devis\n";
    echo "✓ Champs Views personnalisés pour ID et Store\n";
    echo "✓ Logs détaillés pour le debugging\n";
    echo "✓ Support des stores multiples\n\n";
    
    echo "🔧 Fichiers modifiés :\n";
    echo "- modules/custom/client_demandes/client_demandes.module\n";
    echo "- modules/custom/client_demandes/src/Plugin/views/field/OrderIdField.php\n";
    echo "- modules/custom/client_demandes/src/Plugin/views/field/StoreNameField.php\n\n";
    
} catch (PDOException $e) {
    echo "Erreur: " . $e->getMessage() . "\n";
}

echo "=== Test terminé ===\n";
