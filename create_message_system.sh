#!/bin/bash

# Script pour créer le système de messages complet
# Usage: ./create_message_system.sh

echo "=== Création du système de messages ==="

# Configuration de la base de données
DB_NAME="drupal"
DB_USER="drupal"
DB_PASS="drupal"
DB_HOST="localhost"

# Fonction pour exécuter une requête SQL
execute_sql() {
    local query="$1"
    local description="$2"
    echo "[$description]"
    echo "Exécution: ${query:0:100}..."
    mysql -h"$DB_HOST" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "$query"
    if [ $? -eq 0 ]; then
        echo "✓ Succès"
    else
        echo "✗ Erreur"
        return 1
    fi
    echo ""
}

# Fonction pour exécuter un fichier SQL
execute_sql_file() {
    local file="$1"
    local description="$2"
    echo "[$description]"
    echo "Exécution du fichier: $file"
    mysql -h"$DB_HOST" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" < "$file"
    if [ $? -eq 0 ]; then
        echo "✓ Succès"
    else
        echo "✗ Erreur"
        return 1
    fi
    echo ""
}

echo "1. Création du template de message..."
execute_sql "INSERT INTO message_template (template, label, description, text, settings, status, langcode) VALUES ('devis_notification', 'Notification de devis', 'Template pour notifier qu\'un nouveau devis a été reçu', 'a:2:{i:0;a:2:{s:5:\"value\";s:19:\"Nouveau devis reçu\";s:6:\"format\";s:10:\"plain_text\";}i:1;a:2:{s:5:\"value\";s:125:\"Vous avez reçu un nouveau devis de [message:field_prestataire_name] pour votre demande \\\"[message:field_demande_title]\\\" d\'un montant de [message:field_devis_price]€.\";s:6:\"format\";s:10:\"plain_text\";}}', 'a:1:{s:12:\"token_replace\";b:1;}', 1, 'fr');" "Template de message"

echo "2. Création des field storages..."

# Storage field_devis_reference
execute_sql "INSERT INTO field_storage_config (id, field_name, entity_type, type, module, active, cardinality, translatable, locked, persist_with_no_fields, custom_storage, settings, status, langcode) VALUES ('message.field_devis_reference', 'field_devis_reference', 'message', 'entity_reference', 'core', 1, 1, 0, 0, 0, 0, 'a:1:{s:11:\"target_type\";s:4:\"node\";}', 1, 'fr');" "Storage devis_reference"

# Storage field_prestataire_name
execute_sql "INSERT INTO field_storage_config (id, field_name, entity_type, type, module, active, cardinality, translatable, locked, persist_with_no_fields, custom_storage, settings, status, langcode) VALUES ('message.field_prestataire_name', 'field_prestataire_name', 'message', 'string', 'core', 1, 1, 0, 0, 0, 0, 'a:2:{s:10:\"max_length\";i:255;s:14:\"case_sensitive\";b:0;}', 1, 'fr');" "Storage prestataire_name"

# Storage field_demande_title
execute_sql "INSERT INTO field_storage_config (id, field_name, entity_type, type, module, active, cardinality, translatable, locked, persist_with_no_fields, custom_storage, settings, status, langcode) VALUES ('message.field_demande_title', 'field_demande_title', 'message', 'string', 'core', 1, 1, 0, 0, 0, 0, 'a:2:{s:10:\"max_length\";i:255;s:14:\"case_sensitive\";b:0;}', 1, 'fr');" "Storage demande_title"

# Storage field_devis_price
execute_sql "INSERT INTO field_storage_config (id, field_name, entity_type, type, module, active, cardinality, translatable, locked, persist_with_no_fields, custom_storage, settings, status, langcode) VALUES ('message.field_devis_price', 'field_devis_price', 'message', 'string', 'core', 1, 1, 0, 0, 0, 0, 'a:2:{s:10:\"max_length\";i:50;s:14:\"case_sensitive\";b:0;}', 1, 'fr');" "Storage devis_price"

echo "3. Création des field configs..."

# Config field_devis_reference
execute_sql "INSERT INTO field_config (id, field_name, entity_type, bundle, field_type, translatable, required, default_value, default_value_callback, settings, status, langcode) VALUES ('message.devis_notification.field_devis_reference', 'field_devis_reference', 'message', 'devis_notification', 'entity_reference', 0, 1, 'a:0:{}', '', 'a:2:{s:7:\"handler\";s:12:\"default:node\";s:16:\"handler_settings\";a:3:{s:14:\"target_bundles\";a:1:{s:5:\"devis\";s:5:\"devis\";}s:4:\"sort\";a:1:{s:5:\"field\";s:5:\"_none\";}s:11:\"auto_create\";b:0;}}', 1, 'fr');" "Config devis_reference"

# Config field_prestataire_name
execute_sql "INSERT INTO field_config (id, field_name, entity_type, bundle, field_type, translatable, required, default_value, default_value_callback, settings, status, langcode) VALUES ('message.devis_notification.field_prestataire_name', 'field_prestataire_name', 'message', 'devis_notification', 'string', 0, 1, 'a:0:{}', '', 'a:2:{s:10:\"max_length\";i:255;s:14:\"case_sensitive\";b:0;}', 1, 'fr');" "Config prestataire_name"

# Config field_demande_title
execute_sql "INSERT INTO field_config (id, field_name, entity_type, bundle, field_type, translatable, required, default_value, default_value_callback, settings, status, langcode) VALUES ('message.devis_notification.field_demande_title', 'field_demande_title', 'message', 'devis_notification', 'string', 0, 1, 'a:0:{}', '', 'a:2:{s:10:\"max_length\";i:255;s:14:\"case_sensitive\";b:0;}', 1, 'fr');" "Config demande_title"

# Config field_devis_price
execute_sql "INSERT INTO field_config (id, field_name, entity_type, bundle, field_type, translatable, required, default_value, default_value_callback, settings, status, langcode) VALUES ('message.devis_notification.field_devis_price', 'field_devis_price', 'message', 'devis_notification', 'string', 0, 1, 'a:0:{}', '', 'a:2:{s:10:\"max_length\";i:50;s:14:\"case_sensitive\";b:0;}', 1, 'fr');" "Config devis_price"

echo "4. Création des tables des champs..."

# Table field_devis_reference
execute_sql "CREATE TABLE IF NOT EXISTS \`message__field_devis_reference\` (
  \`bundle\` varchar(128) CHARACTER SET ascii COLLATE ascii_general_ci NOT NULL DEFAULT '' COMMENT 'The field instance bundle to which this row belongs, used when deleting a field instance',
  \`deleted\` tinyint NOT NULL DEFAULT 0 COMMENT 'A boolean indicating whether this data item has been deleted',
  \`entity_id\` int unsigned NOT NULL COMMENT 'The entity id this data is attached to',
  \`revision_id\` int unsigned NOT NULL COMMENT 'The entity revision id this data is attached to',
  \`langcode\` varchar(32) CHARACTER SET ascii COLLATE ascii_general_ci NOT NULL DEFAULT '' COMMENT 'The language code for this data item.',
  \`delta\` int unsigned NOT NULL COMMENT 'The sequence number for this data item, used for multi-value fields',
  \`field_devis_reference_target_id\` int unsigned NOT NULL COMMENT 'The ID of the target entity.',
  PRIMARY KEY (\`entity_id\`,\`deleted\`,\`delta\`,\`langcode\`),
  KEY \`bundle\` (\`bundle\`),
  KEY \`revision_id\` (\`revision_id\`),
  KEY \`field_devis_reference_target_id\` (\`field_devis_reference_target_id\`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Data storage for message field field_devis_reference.';" "Table devis_reference"

# Table field_prestataire_name
execute_sql "CREATE TABLE IF NOT EXISTS \`message__field_prestataire_name\` (
  \`bundle\` varchar(128) CHARACTER SET ascii COLLATE ascii_general_ci NOT NULL DEFAULT '' COMMENT 'The field instance bundle to which this row belongs, used when deleting a field instance',
  \`deleted\` tinyint NOT NULL DEFAULT 0 COMMENT 'A boolean indicating whether this data item has been deleted',
  \`entity_id\` int unsigned NOT NULL COMMENT 'The entity id this data is attached to',
  \`revision_id\` int unsigned NOT NULL COMMENT 'The entity revision id this data is attached to',
  \`langcode\` varchar(32) CHARACTER SET ascii COLLATE ascii_general_ci NOT NULL DEFAULT '' COMMENT 'The language code for this data item.',
  \`delta\` int unsigned NOT NULL COMMENT 'The sequence number for this data item, used for multi-value fields',
  \`field_prestataire_name_value\` varchar(255) NOT NULL,
  PRIMARY KEY (\`entity_id\`,\`deleted\`,\`delta\`,\`langcode\`),
  KEY \`bundle\` (\`bundle\`),
  KEY \`revision_id\` (\`revision_id\`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Data storage for message field field_prestataire_name.';" "Table prestataire_name"

# Table field_demande_title
execute_sql "CREATE TABLE IF NOT EXISTS \`message__field_demande_title\` (
  \`bundle\` varchar(128) CHARACTER SET ascii COLLATE ascii_general_ci NOT NULL DEFAULT '' COMMENT 'The field instance bundle to which this row belongs, used when deleting a field instance',
  \`deleted\` tinyint NOT NULL DEFAULT 0 COMMENT 'A boolean indicating whether this data item has been deleted',
  \`entity_id\` int unsigned NOT NULL COMMENT 'The entity id this data is attached to',
  \`revision_id\` int unsigned NOT NULL COMMENT 'The entity revision id this data is attached to',
  \`langcode\` varchar(32) CHARACTER SET ascii COLLATE ascii_general_ci NOT NULL DEFAULT '' COMMENT 'The language code for this data item.',
  \`delta\` int unsigned NOT NULL COMMENT 'The sequence number for this data item, used for multi-value fields',
  \`field_demande_title_value\` varchar(255) NOT NULL,
  PRIMARY KEY (\`entity_id\`,\`deleted\`,\`delta\`,\`langcode\`),
  KEY \`bundle\` (\`bundle\`),
  KEY \`revision_id\` (\`revision_id\`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Data storage for message field field_demande_title.';" "Table demande_title"

# Table field_devis_price
execute_sql "CREATE TABLE IF NOT EXISTS \`message__field_devis_price\` (
  \`bundle\` varchar(128) CHARACTER SET ascii COLLATE ascii_general_ci NOT NULL DEFAULT '' COMMENT 'The field instance bundle to which this row belongs, used when deleting a field instance',
  \`deleted\` tinyint NOT NULL DEFAULT 0 COMMENT 'A boolean indicating whether this data item has been deleted',
  \`entity_id\` int unsigned NOT NULL COMMENT 'The entity id this data is attached to',
  \`revision_id\` int unsigned NOT NULL COMMENT 'The entity revision id this data is attached to',
  \`langcode\` varchar(32) CHARACTER SET ascii COLLATE ascii_general_ci NOT NULL DEFAULT '' COMMENT 'The language code for this data item.',
  \`delta\` int unsigned NOT NULL COMMENT 'The sequence number for this data item, used for multi-value fields',
  \`field_devis_price_value\` varchar(50) NOT NULL,
  PRIMARY KEY (\`entity_id\`,\`deleted\`,\`delta\`,\`langcode\`),
  KEY \`bundle\` (\`bundle\`),
  KEY \`revision_id\` (\`revision_id\`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Data storage for message field field_devis_price.';" "Table devis_price"

echo "5. Création d'un message de test..."
execute_sql "INSERT INTO message (template, uid, created, changed, langcode) VALUES ('devis_notification', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'fr');" "Message de test"

# Récupérer l'ID du message et ajouter les champs
execute_sql "SET @message_id = LAST_INSERT_ID();
INSERT INTO message__field_devis_reference (bundle, deleted, entity_id, revision_id, langcode, delta, field_devis_reference_target_id) VALUES ('devis_notification', 0, @message_id, @message_id, 'fr', 0, 1);
INSERT INTO message__field_prestataire_name (bundle, deleted, entity_id, revision_id, langcode, delta, field_prestataire_name_value) VALUES ('devis_notification', 0, @message_id, @message_id, 'fr', 0, 'Test Prestataire');
INSERT INTO message__field_demande_title (bundle, deleted, entity_id, revision_id, langcode, delta, field_demande_title_value) VALUES ('devis_notification', 0, @message_id, @message_id, 'fr', 0, 'Test Demande');
INSERT INTO message__field_devis_price (bundle, deleted, entity_id, revision_id, langcode, delta, field_devis_price_value) VALUES ('devis_notification', 0, @message_id, @message_id, 'fr', 0, '150.00');" "Données du message de test"

echo "6. Vider le cache Drupal..."
if command -v drush &> /dev/null; then
    drush cr
    echo "✓ Cache vidé avec Drush"
else
    echo "⚠ Drush non trouvé, videz le cache manuellement"
fi

echo "7. Vérification de la création..."
execute_sql "SELECT COUNT(*) as nb_templates FROM message_template WHERE template = 'devis_notification';" "Vérification template"
execute_sql "SELECT COUNT(*) as nb_configs FROM field_config WHERE entity_type = 'message' AND bundle = 'devis_notification';" "Vérification configs"
execute_sql "SELECT COUNT(*) as nb_messages FROM message WHERE template = 'devis_notification';" "Vérification messages"

echo ""
echo "=== Création terminée ==="
echo "Le système de messages a été créé avec succès."
echo "Vous pouvez maintenant tester l'envoi de devis."
