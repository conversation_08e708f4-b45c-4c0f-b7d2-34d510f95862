<?php

/**
 * Script pour diagnostiquer l'erreur de contexte 'entity:user'
 */

use Drupal\Core\DrupalKernel;
use Symfony\Component\HttpFoundation\Request;

$autoloader = require_once 'autoload.php';

$kernel = new DrupalKernel('prod', $autoloader);
$request = Request::createFromGlobals();
$kernel->setSitePath('sites/default');
$kernel->boot();
$kernel->preHandle($request);

// Initialiser le container Drupal
$container = \Drupal::getContainer();

echo "=== Diagnostic de l'erreur de contexte 'entity:user' ===\n\n";

// 1. Vérifier les blocs actifs
echo "1. Blocs actifs :\n";
$database = \Drupal::database();
$query = $database->select('config', 'c')
  ->fields('c', ['name', 'data'])
  ->condition('name', 'block.block.%', 'LIKE');
$results = $query->execute();

foreach ($results as $record) {
  $data = unserialize($record->data);
  if (isset($data['status']) && $data['status'] && isset($data['visibility']) && !empty($data['visibility'])) {
    echo "  - Bloc: {$record->name}\n";
    foreach ($data['visibility'] as $condition_id => $condition_config) {
      echo "    Condition: {$condition_id}\n";
      if (isset($condition_config['context_mapping'])) {
        foreach ($condition_config['context_mapping'] as $context_name => $context_mapping) {
          echo "      Contexte mappé: {$context_name} -> {$context_mapping}\n";
          if (strpos($context_mapping, 'entity:user') !== false) {
            echo "        *** PROBLÈME POTENTIEL: Contexte entity:user requis ***\n";
          }
        }
      }
    }
    echo "\n";
  }
}

// 2. Vérifier les conditions de plugin actives
echo "\n2. Conditions de plugin qui nécessitent entity:user :\n";
$condition_manager = \Drupal::service('plugin.manager.condition');
$definitions = $condition_manager->getDefinitions();

foreach ($definitions as $plugin_id => $definition) {
  if (isset($definition['context_definitions'])) {
    foreach ($definition['context_definitions'] as $context_name => $context_definition) {
      if (isset($context_definition['type']) && $context_definition['type'] === 'entity:user') {
        echo "  - Plugin: {$plugin_id}\n";
        echo "    Contexte: {$context_name} (entity:user)\n";
      }
    }
  }
}

// 3. Vérifier les routes avec paramètres entity:user
echo "\n3. Routes avec paramètres entity:user :\n";
$route_provider = \Drupal::service('router.route_provider');
$routes = $route_provider->getAllRoutes();

foreach ($routes as $route_name => $route) {
  $options = $route->getOptions();
  if (isset($options['parameters'])) {
    foreach ($options['parameters'] as $param_name => $param_config) {
      if (isset($param_config['type']) && $param_config['type'] === 'entity:user') {
        echo "  - Route: {$route_name}\n";
        echo "    Path: {$route->getPath()}\n";
        echo "    Paramètre: {$param_name} (entity:user)\n";

        // Vérifier si le paramètre est dans le chemin
        if (strpos($route->getPath(), "{{$param_name}}") === false) {
          echo "    *** PROBLÈME: Paramètre entity:user défini mais pas dans le chemin ***\n";
        }
        echo "\n";
      }
    }
  }
}

echo "\n=== Fin du diagnostic ===\n";
