#!/bin/bash

echo "=== Diagnostic de l'erreur de contexte 'entity:user' ==="
echo ""

# 1. Vérifier les routes avec entity:user mais sans paramètre dans le chemin
echo "1. Routes avec problème de paramètre entity:user :"
echo "   (paramètre entity:user défini mais pas dans le chemin de la route)"
echo ""

# Chercher dans tous les fichiers de routage
find . -name "*.routing.yml" -exec grep -l "entity:user" {} \; | while read file; do
  echo "Fichier: $file"
  
  # Extraire les routes avec entity:user
  awk '
  /^[a-zA-Z_][a-zA-Z0-9_]*:/ { 
    route_name = $1; 
    gsub(/:$/, "", route_name);
    in_route = 1; 
    path = ""; 
    has_entity_user = 0;
    user_param = "";
  }
  /^[a-zA-Z_]/ && !/^  / { in_route = 0; }
  in_route && /^  path:/ { 
    path = $0; 
    gsub(/^  path: ./, "", path);
    gsub(/./, "", path);
  }
  in_route && /type: entity:user/ { 
    has_entity_user = 1;
    # Récupérer le nom du paramètre (ligne précédente)
    getline prev_line < "/dev/stdin";
    # Cette approche ne fonctionne pas bien, utilisons une autre méthode
  }
  in_route && /^      [a-zA-Z_][a-zA-Z0-9_]*:$/ {
    param_name = $1;
    gsub(/:$/, "", param_name);
    gsub(/^      /, "", param_name);
    getline;
    if ($0 ~ /type: entity:user/) {
      user_param = param_name;
      has_entity_user = 1;
    }
  }
  END {
    if (has_entity_user && user_param != "" && path != "") {
      placeholder = "{" user_param "}";
      if (index(path, placeholder) == 0) {
        print "  PROBLÈME: Route " route_name;
        print "    Path: " path;
        print "    Paramètre entity:user: " user_param;
        print "    Le paramètre {" user_param "} n est pas dans le chemin!";
        print "";
      }
    }
  }
  ' "$file"
done

echo ""
echo "2. Recherche de blocs avec conditions de visibilité problématiques :"
echo ""

# Chercher dans les fichiers de configuration de blocs
find . -path "*/config*" -name "block.block.*.yml" -exec grep -l "visibility:" {} \; | while read file; do
  if grep -q "context_mapping" "$file"; then
    echo "Bloc avec context_mapping: $file"
    grep -A 10 -B 2 "context_mapping" "$file"
    echo ""
  fi
done

echo ""
echo "3. Modules qui définissent des conditions nécessitant entity:user :"
echo ""

# Chercher les plugins de condition qui nécessitent entity:user
find . -name "*.php" -exec grep -l "context_definitions.*entity:user" {} \; | while read file; do
  echo "Fichier: $file"
  grep -A 5 -B 5 "entity:user" "$file"
  echo ""
done

echo ""
echo "=== Fin du diagnostic ==="
