<?php

/**
 * Script pour désactiver temporairement les blocs problématiques
 */

// Connexion directe à la base de données
$host = '127.0.0.1';
$dbname = 'juillet2025';
$username = 'root';
$password = 'password';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== Désactivation temporaire des blocs problématiques ===\n\n";
    
    // Blocs à désactiver temporairement
    $blocks_to_disable = [
        'block.block.bootstrap5_menuadmin',
        'block.block.bootstrap5_menuseo',
        'block.block.bootstrap5_resetcache'
    ];
    
    foreach ($blocks_to_disable as $block_name) {
        echo "Désactivation du bloc: $block_name\n";
        
        $stmt = $pdo->prepare("SELECT data FROM config WHERE name = ?");
        $stmt->execute([$block_name]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result) {
            $data = unserialize($result['data']);
            
            if ($data['status'] === true) {
                // Créer une sauvegarde de l'état actuel
                $backup_name = $block_name . '_backup_active_state_' . date('Y_m_d_H_i_s');
                $stmt = $pdo->prepare("INSERT INTO config (name, data, collection) VALUES (?, ?, '')");
                $stmt->execute([$backup_name, serialize($data)]);
                
                // Désactiver le bloc
                $data['status'] = false;
                
                $stmt = $pdo->prepare("UPDATE config SET data = ? WHERE name = ?");
                $stmt->execute([serialize($data), $block_name]);
                
                echo "  ✓ Bloc désactivé\n";
                echo "  ✓ Sauvegarde créée: $backup_name\n";
            } else {
                echo "  - Bloc déjà désactivé\n";
            }
        } else {
            echo "  - Bloc non trouvé\n";
        }
        echo "\n";
    }
    
    // Vider le cache
    echo "Vidage du cache...\n";
    $cache_tables = ['cache_bootstrap', 'cache_config', 'cache_container', 'cache_data', 'cache_default', 'cache_discovery', 'cache_dynamic_page_cache', 'cache_entity', 'cache_menu', 'cache_page', 'cache_render', 'cache_toolbar'];
    
    foreach ($cache_tables as $table) {
        try {
            $stmt = $pdo->prepare("DELETE FROM $table");
            $stmt->execute();
        } catch (PDOException $e) {
            // Continue if table doesn't exist
        }
    }
    echo "  ✓ Cache vidé\n\n";
    
    echo "=== Test du site ===\n";
    echo "Testez maintenant votre site. Si l'erreur disparaît, le problème vient bien de ces blocs.\n";
    echo "Pour réactiver les blocs plus tard :\n";
    echo "UPDATE config SET data = (SELECT data FROM config WHERE name LIKE '%_backup_active_state_%' AND name LIKE '%nom_du_bloc%' ORDER BY name DESC LIMIT 1) WHERE name = 'nom_du_bloc';\n\n";
    
} catch (PDOException $e) {
    echo "Erreur: " . $e->getMessage() . "\n";
}
