<?php

/**
 * <PERSON>ript pour patcher temporairement Context.php pour obtenir une trace
 */

echo "=== Patch de debug pour Context.php ===\n\n";

$context_file = 'core/lib/Drupal/Core/Plugin/Context/Context.php';

if (!file_exists($context_file)) {
    echo "Erreur: Fichier Context.php non trouvé\n";
    exit(1);
}

// Lire le contenu actuel
$content = file_get_contents($context_file);

// Créer une sauvegarde
$backup_file = $context_file . '.backup.' . date('Y_m_d_H_i_s');
file_put_contents($backup_file, $content);
echo "Sauvegarde créée: $backup_file\n";

// Rechercher la méthode getContextValue et ajouter du debug
$old_method = '  public function getContextValue() {
    if (!$this->hasContextValue()) {
      $this->setContextValue($this->getContextData());
    }
    return $this->contextValue;
  }';

$new_method = '  public function getContextValue() {
    if (!$this->hasContextValue()) {
      // Debug: Log quand le contexte n\'est pas disponible
      if ($this->contextDefinition->getDataType() === \'entity:user\') {
        error_log("DEBUG: Tentative d\'accès au contexte entity:user non disponible");
        error_log("DEBUG: Trace: " . print_r(debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 10), true));
      }
      $this->setContextValue($this->getContextData());
    }
    return $this->contextValue;
  }';

// Remplacer le contenu
$new_content = str_replace($old_method, $new_method, $content);

if ($new_content === $content) {
    echo "Attention: Aucune modification détectée. Essayons une approche différente...\n";
    
    // Essayer de trouver la ligne 73 spécifiquement
    $lines = explode("\n", $content);
    for ($i = 70; $i < 80; $i++) {
        if (isset($lines[$i])) {
            echo "Ligne " . ($i + 1) . ": " . $lines[$i] . "\n";
        }
    }
    
    // Chercher la méthode qui contient "throw new ContextException"
    if (strpos($content, 'throw new ContextException') !== false) {
        echo "\nTrouvé 'throw new ContextException' dans le fichier\n";
        
        // Ajouter du debug avant l'exception
        $old_exception = 'throw new ContextException';
        $new_exception = 'error_log("DEBUG Context: Contexte requis non présent - " . $this->contextDefinition->getDataType() . " - Trace: " . print_r(debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 5), true)); throw new ContextException';
        
        $new_content = str_replace($old_exception, $new_exception, $content);
        
        if ($new_content !== $content) {
            echo "Patch d'exception appliqué\n";
        }
    }
} else {
    echo "Patch de méthode appliqué\n";
}

if ($new_content !== $content) {
    // Écrire le nouveau contenu
    file_put_contents($context_file, $new_content);
    echo "Patch appliqué avec succès\n";
    
    // Vider le cache
    echo "\nVidage du cache...\n";
    $cache_dirs = ['sites/default/files/php'];
    foreach ($cache_dirs as $dir) {
        if (is_dir($dir)) {
            $iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
                RecursiveIteratorIterator::CHILD_FIRST
            );
            
            foreach ($iterator as $file) {
                if ($file->isFile()) {
                    unlink($file->getRealPath());
                }
            }
            echo "Cache PHP vidé: $dir\n";
        }
    }
    
    echo "\n=== Patch terminé ===\n";
    echo "Faites maintenant une requête sur le site et vérifiez les logs.\n";
    echo "Pour annuler le patch :\n";
    echo "cp $backup_file $context_file\n\n";
} else {
    echo "Aucune modification appliquée\n";
}
