opentimestamp.form:
  path: '/opentimestamp/form'
  defaults:
    _form: '\Drupal\opentimestamp\Form\TimestampForm'
    _title: 'Horodater un fichier'
  requirements:
    _permission: 'access opentimestamp'

opentimestamp.admin_manual_form:
  path: '/admin/opentimestamp/manual'
  defaults:
    _form: '\Drupal\opentimestamp\Form\AdminManualTimestampForm'
    _title: 'Horodatage manuel'
  requirements:
    _permission: 'administer opentimestamp'
  options:
    _admin_route: TRUE

opentimestamp.admin_manual_timestamp:
  path: '/admin/opentimestamp/manual-timestamp'
  defaults:
    _controller: '\Drupal\opentimestamp\Controller\RedirectController::redirectToAdminManual'
    _title: 'Horodatage manuel'
  requirements:
    _permission: 'administer opentimestamp'
  options:
    _admin_route: TRUE

opentimestamp.manual_timestamp:
  path: '/node/{node}/timestamp'
  defaults:
    _controller: '\Drupal\opentimestamp\Controller\ManualTimestampController::stamp'
    _title: 'Horodater le contenu'
  requirements:
    _permission: 'timestamp content'
    _timestamp_access: 'TRUE'
  options:
    parameters:
      node:
        type: entity:node

opentimestamp.verify:
  path: '/opentimestamp/verify/{fid}'
  defaults:
    _controller: '\Drupal\opentimestamp\Controller\VerifyController::verify'
    _title: 'Vérifier lorodatage'
  requirements:
    _permission: 'access content'
    fid: '\d+'

opentimestamp.download_ots:
  path: '/opentimestamp/download-ots/{fid}'
  defaults:
    _controller: '\Drupal\opentimestamp\Controller\VerifyController::downloadOts'
    _title: 'Télécharger le fichier OTS'
  requirements:
    _permission: 'access content'
    fid: '\d+'



opentimestamp.upgrade:
  path: '/opentimestamp/upgrade/{fid}'
  defaults:
    _controller: '\Drupal\opentimestamp\Controller\UpgradeController::upgrade'
    _title: 'Mettre à jour le horodatage'
  requirements:
    _permission: 'access opentimestamp'
  options:
    parameters:
      fid:
        type: integer



opentimestamp.history:
  path: '/opentimestamp/history'
  defaults:
    _controller: '\Drupal\opentimestamp\Controller\HistoryController::historyPage'
    _title: 'Voir toutes les attestations'
  requirements:
    _permission: 'access opentimestamp history'



opentimestamp.node_history:
  path: '/node/{node}/timestamps'
  defaults:
    _controller: '\Drupal\opentimestamp\Controller\HistoryController::nodeHistoryPage'
    _title: 'Historique des horodatages'
  requirements:
    _permission: 'view timestamps'
  options:
    parameters:
      node:
        type: entity:node

opentimestamp.settings:
  path: '/admin/config/system/opentimestamp'
  defaults:
    _form: '\Drupal\opentimestamp\Form\SettingsForm'
    _title: 'OpenTimestamp Settings'
  requirements:
    _permission: 'administer opentimestamp'
  options:
    _admin_route: TRUE



opentimestamp.current_user_timestamps:
  path: '/user/timestamps'
  defaults:
    _controller: '\Drupal\opentimestamp\Controller\UserTimestampController::currentUserTimestamps'
    _title: 'Mes horodatages'
  requirements:
    _permission: 'access opentimestamp'

opentimestamp.send_attestation:
  path: '/opentimestamp/send-attestation/{fid}'
  defaults:
    _controller: '\Drupal\opentimestamp\Controller\HistoryController::sendAttestation'
    _title: 'Envoyer attestation'
  requirements:
    _permission: 'access content'
  options:
    parameters:
      fid:
        type: integer


