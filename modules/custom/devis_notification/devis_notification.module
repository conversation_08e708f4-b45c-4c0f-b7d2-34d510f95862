<?php

/**
 * @file
 * Module pour gérer les notifications de devis.
 */

use Drupal\Core\Entity\EntityInterface;
use Drupal\commerce_order\Entity\OrderInterface;
use Drupal\Core\Mail\MailManagerInterface;
use Drupal\Core\Language\LanguageManagerInterface;

/**
 * Implements hook_entity_insert().
 */
function devis_notification_entity_insert(EntityInterface $entity) {
  // Vérifier si c'est une commande (devis)
  if ($entity instanceof OrderInterface) {
    // Vérifier si c'est un devis (état Draft)
    if ($entity->getState()->getId() === 'draft') {
      devis_notification_send_email($entity);
    }
  }
}

/**
 * Implements hook_entity_update().
 */
function devis_notification_entity_update(EntityInterface $entity) {
  // Vérifier si c'est une commande qui passe en état draft
  if ($entity instanceof OrderInterface) {
    $original = $entity->original ?? NULL;
    
    // Si l'état change vers draft, envoyer l'email
    if ($entity->getState()->getId() === 'draft' && 
        ($original === NULL || $original->getState()->getId() !== 'draft')) {
      devis_notification_send_email($entity);
    }
  }
}

/**
 * Envoie un email de notification de devis au client.
 */
function devis_notification_send_email(OrderInterface $order) {
  $customer = $order->getCustomer();
  
  // Vérifier que le client a une adresse email
  if (!$customer || !$customer->getEmail()) {
    \Drupal::logger('devis_notification')->warning('Impossible d\'envoyer l\'email de devis : client sans email pour la commande @order_id', [
      '@order_id' => $order->id(),
    ]);
    return;
  }

  $mail_manager = \Drupal::service('plugin.manager.mail');
  $language_manager = \Drupal::service('language_manager');
  $language = $language_manager->getDefaultLanguage();

  $params = [
    'order' => $order,
    'customer' => $customer,
    'store' => $order->getStore(),
  ];

  $result = $mail_manager->mail(
    'devis_notification',
    'devis_created',
    $customer->getEmail(),
    $language->getId(),
    $params,
    NULL,
    TRUE
  );

  if ($result['result']) {
    \Drupal::logger('devis_notification')->info('Email de devis envoyé à @email pour la commande @order_id', [
      '@email' => $customer->getEmail(),
      '@order_id' => $order->id(),
    ]);
  } else {
    \Drupal::logger('devis_notification')->error('Échec de l\'envoi de l\'email de devis à @email pour la commande @order_id', [
      '@email' => $customer->getEmail(),
      '@order_id' => $order->id(),
    ]);
  }
}

/**
 * Implements hook_mail().
 */
function devis_notification_mail($key, &$message, $params) {
  switch ($key) {
    case 'devis_created':
      $order = $params['order'];
      $customer = $params['customer'];
      $store = $params['store'];
      
      $message['subject'] = t('Nouveau devis #@order_id - @store_name', [
        '@order_id' => $order->id(),
        '@store_name' => $store->getName(),
      ]);
      
      $message['body'][] = t('Bonjour @customer_name,', [
        '@customer_name' => $customer->getDisplayName(),
      ]);
      
      $message['body'][] = t('Un nouveau devis a été créé pour vous :');
      $message['body'][] = '';
      $message['body'][] = t('Numéro de devis : #@order_id', [
        '@order_id' => $order->id(),
      ]);
      $message['body'][] = t('Store : @store_name', [
        '@store_name' => $store->getName(),
      ]);
      $message['body'][] = t('Montant total : @total', [
        '@total' => $order->getTotalPrice() ? $order->getTotalPrice()->__toString() : '0,00 €',
      ]);
      $message['body'][] = '';
      $message['body'][] = t('Vous pouvez consulter votre devis en vous connectant à votre compte.');
      $message['body'][] = '';
      $message['body'][] = t('Cordialement,');
      $message['body'][] = t('L\'équipe @store_name', [
        '@store_name' => $store->getName(),
      ]);
      break;
  }
}

/**
 * Implements hook_views_data_alter().
 */
function devis_notification_views_data_alter(array &$data) {
  // Ajouter des champs personnalisés pour les vues de commandes
  if (isset($data['commerce_order'])) {
    $data['commerce_order']['order_id_display'] = [
      'title' => t('Order ID Display'),
      'help' => t('Displays the order ID with formatting.'),
      'field' => [
        'id' => 'devis_notification_order_id',
      ],
    ];
    
    $data['commerce_order']['store_name_display'] = [
      'title' => t('Store Name Display'),
      'help' => t('Displays the store name for the order.'),
      'field' => [
        'id' => 'devis_notification_store_name',
      ],
    ];
  }
}
