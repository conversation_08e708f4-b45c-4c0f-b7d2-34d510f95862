<?php

namespace Drupal\devis_notification\Plugin\views\field;

use Drupal\views\Plugin\views\field\FieldPluginBase;
use <PERSON>upal\views\ResultRow;

/**
 * Field handler to display order ID with formatting.
 *
 * @ViewsField("devis_notification_order_id")
 */
class OrderIdField extends FieldPluginBase {

  /**
   * {@inheritdoc}
   */
  public function query() {
    // Ne pas ajouter de requête, nous utilisons les données existantes.
  }

  /**
   * {@inheritdoc}
   */
  public function render(ResultRow $values) {
    $order = $this->getEntity($values);
    
    if ($order && $order->id()) {
      return [
        '#markup' => '#' . $order->id(),
      ];
    }
    
    return '';
  }

}
