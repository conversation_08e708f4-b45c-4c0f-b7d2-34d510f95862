<?php

namespace Drupal\client_demandes\Access;

use Drupal\Core\Access\AccessResult;
use <PERSON>upal\Core\Session\AccountInterface;
use <PERSON><PERSON><PERSON>\node\NodeInterface;

/**
 * Vérifie l'accès aux demandes pour les prestataires.
 */
class DemandeAccessCheck
{

  /**
   * Vérifie si un prestataire peut proposer un devis sur une demande.
   *
   * @param \Drupal\Core\Session\AccountInterface $account
   *   Le compte utilisateur à vérifier.
   * @param \Drupal\node\NodeInterface $node
   *   Le nœud de la demande.
   *
   * @return \Drupal\Core\Access\AccessResultInterface
   *   Le résultat de l'accès.
   */
  public static function checkDevisAccess(AccountInterface $account, NodeInterface $node)
  {
    // Vérifier que le nœud est une demande
    if ($node->getType() != 'demande') {
      return AccessResult::forbidden('Ce n\'est pas une demande.');
    }

    // Vérifier que la demande est ouverte
    if ($node->hasField('field_statut_demande') && $node->get('field_statut_demande')->value != 'ouverte') {
      return AccessResult::forbidden('Cette demande n\'est plus ouverte.');
    }

    // Vérifier que l'utilisateur a le rôle prestataire
    if (!$account->hasRole('prestataire')) {
      return AccessResult::forbidden('Vous devez être un prestataire pour proposer un devis.');
    }

    // Vérifier que l'utilisateur n'est pas le propriétaire de la demande
    if ($node->getOwnerId() == $account->id()) {
      return AccessResult::forbidden('Vous ne pouvez pas proposer un devis sur votre propre demande.');
    }

    // Vérifier si le prestataire a déjà proposé un devis pour cette demande
    $query = \Drupal::entityQuery('node')
      ->condition('type', 'devis')
      ->condition('uid', $account->id())
      ->condition('field_demande_ref', $node->id())
      ->accessCheck(TRUE);
    $result = $query->execute();

    if (!empty($result)) {
      return AccessResult::forbidden('Vous avez déjà proposé un devis pour cette demande.');
    }

    // Accès autorisé
    return AccessResult::allowed();
  }
}
