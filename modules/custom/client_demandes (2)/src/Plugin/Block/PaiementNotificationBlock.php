<?php

namespace Drupal\client_demandes\Plugin\Block;

use Drupal\Core\Block\BlockBase;
use Drupal\Core\Session\AccountInterface;
use Drupal\commerce_order\Entity\Order;

/**
 * Provides a 'PaiementNotificationBlock' block.
 *
 * @Block(
 *   id = "paiement_notification_block",
 *   admin_label = @Translation("Notification de paiement"),
 * )
 */
class PaiementNotificationBlock extends BlockBase {

  /**
   * {@inheritdoc}
   */
  public function build() {
    $account = \Drupal::currentUser();
    $output = ['#markup' => ''];

    // Rechercher les dernières commandes dont l'auteur du produit est ce prestataire
    $query = \Drupal::entityQuery('commerce_order')
      ->accessCheck(TRUE)
      ->sort('created', 'DESC')
      ->range(0, 10);
    $order_ids = $query->execute();
    if (!$order_ids) return $output;

    $notifications = [];
    foreach (Order::loadMultiple($order_ids) as $order) {
      foreach ($order->getItems() as $item) {
        $product = $item->getPurchasedEntity();
        if ($product && $product->getOwnerId() == $account->id()) {
          $status = $order->getState()->value;
          if ($status === 'completed') {
            $notifications[] = "🟢 Paiement reçu pour la commande #" . $order->id();
          }
        }
      }
    }

    if ($notifications) {
      $output['#theme'] = 'item_list';
      $output['#items'] = $notifications;
    }

    return $output;
  }

}
