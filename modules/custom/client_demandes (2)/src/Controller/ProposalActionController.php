<?php

namespace Drupal\client_demandes\Controller;

use <PERSON><PERSON>al\Core\Controller\ControllerBase;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Drupal\node\Entity\Node;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Drupal\user\Entity\User;
use Drupal\Core\Url;

class ProposalActionController extends ControllerBase {

  protected $currentUser;

  public function __construct() {
    $this->currentUser = \Drupal::currentUser();
  }

  public static function create(ContainerInterface $container) {
    return new static();
  }

  /**
   * Page avec deux choix : accepter ou refuser la proposition
   */
  public function viewProposal($devis_nid) {
    $devis = Node::load($devis_nid);
    if (!$devis || $devis->bundle() !== 'devis') {
      throw new \Symfony\Component\HttpKernel\Exception\NotFoundHttpException();
    }

    $build = [
      '#markup' => '<h2>Souhaitez-vous accepter ce devis ?</h2>',
    ];

    $build['accept'] = [
      '#type' => 'link',
      '#title' => $this->t('✅ Accepter la proposition'),
      '#url' => Url::fromRoute('client_demandes.proposal_accept', ['devis_nid' => $devis_nid]),
      '#attributes' => ['class' => ['button', 'button--primary']],
    ];

    $build['refuse'] = [
      '#type' => 'link',
      '#title' => $this->t('❌ Refuser'),
      '#url' => Url::fromRoute('client_demandes.proposal_refuse', ['devis_nid' => $devis_nid]),
      '#attributes' => ['class' => ['button', 'button--danger']],
    ];

    return $build;
  }

  /**
   * Refus du devis
   */
  public function refuse($devis_nid) {
    $devis = Node::load($devis_nid);
    if ($devis && $devis->bundle() === 'devis') {
      $devis->set('field_statut_devis', 'refuse');
      $devis->save();
      $this->messenger()->addStatus($this->t('Vous avez refusé cette proposition.'));
    }
    return new RedirectResponse('/mon-espace/mes-demandes');
  }

  /**
   * Page de choix de paiement après acceptation
   */
  public function accept($devis_nid) {
    $devis = Node::load($devis_nid);
    if (!$devis || $devis->bundle() !== 'devis') {
      throw new \Symfony\Component\HttpKernel\Exception\NotFoundHttpException();
    }

    $prestataire = User::load($devis->getOwnerId());
    $forced_mode = \Drupal::config('client_demandes.settings')->get('mode_paiement_force');
    $default_mode = $prestataire->get('field_mode_paiement_par_defaut')->value;
    $mode = $forced_mode ?: $default_mode;

    // Affiche le choix si non forcé
    if (!$forced_mode) {
      return [
        '#markup' => '<h2>Choisissez votre mode de paiement</h2>',
        'complet' => [
          '#type' => 'link',
          '#title' => $this->t('Paiement complet au prestataire'),
          '#url' => Url::fromRoute('client_demandes.stripe_checkout', [
            'devis_nid' => $devis_nid,
            'mode' => 'complet'
          ]),
        ],
        'multi' => [
          '#type' => 'link',
          '#title' => $this->t('Paiement partagé (plateforme + prestataire)'),
          '#url' => Url::fromRoute('client_demandes.stripe_checkout', [
            'devis_nid' => $devis_nid,
            'mode' => 'multi'
          ]),
        ],
      ];
    }

    // Redirection directe vers le checkout avec le mode forcé
    return new RedirectResponse(Url::fromRoute('client_demandes.stripe_checkout', [
      'devis_nid' => $devis_nid,
      'mode' => $mode,
    ])->toString());
  }
}
