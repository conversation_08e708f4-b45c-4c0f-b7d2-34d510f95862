<?php

namespace Drupal\client_demandes\Controller;

use Drupal\Core\Controller\ControllerBase;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Drupal\commerce_order\Entity\Order;
use Dompdf\Dompdf;
use Drupal\file\Entity\File;

class StripeWebhookController extends ControllerBase {

  public function facture($commerce_order) {
    $order = Order::load($commerce_order);
    if (!$order) {
      return new Response('Commande introuvable', 404);
    }

    $client = $order->getCustomer()->getDisplayName();
    $client_mail = $order->getEmail();
    $amount = $order->getTotalPrice()->__toString();
    $date = date('d/m/Y');
    $order_id = $order->id();

    $html = \Drupal::service('renderer')->renderPlain([
      '#theme' => 'invoice',
      '#order_id' => $order_id,
      '#client' => $client,
      '#amount' => $amount,
      '#date' => $date,
    ]);

    $dompdf = new Dompdf();
    $dompdf->loadHtml($html);
    $dompdf->setPaper('A4', 'portrait');
    $dompdf->render();

    $pdf_output = $dompdf->output();

    $file_path = 'public://factures/facture_' . $order_id . '.pdf';
    file_prepare_directory('public://factures', FILE_CREATE_DIRECTORY | FILE_MODIFY_PERMISSIONS);
    file_unmanaged_save_data($pdf_output, $file_path, FILE_EXISTS_REPLACE);

    // Envoyer l'email au client
    $module = 'client_demandes';
    $key = 'facture_envoyee';
    $to = $client_mail;
    $params['subject'] = 'Votre facture - Commande #' . $order_id;
    $params['body'] = 'Bonjour,\n\nVeuillez trouver en pièce jointe votre facture.\n\nMerci.';
    $params['attachments'][] = [
      'filepath' => $file_path,
      'filename' => 'facture_' . $order_id . '.pdf',
      'filemime' => 'application/pdf',
    ];
    $langcode = \Drupal::currentUser()->getPreferredLangcode();
    $send = TRUE;
    \Drupal::service('plugin.manager.mail')->mail($module, $key, $to, $langcode, $params, NULL, $send);

    // Envoi au prestataire si identifié (auteur du produit commandé)
    if ($order->getItems()) {
      $item = $order->getItems()[0];
      $product = $item->getPurchasedEntity();
      if ($product && $product->getOwner()) {
        $prestataire = $product->getOwner();
        if ($prestataire->getEmail()) {
          \Drupal::service('plugin.manager.mail')->mail($module, $key, $prestataire->getEmail(), $langcode, $params, NULL, $send);
        }
      }
    }

    return new BinaryFileResponse($file_path, 200, [
      'Content-Type' => 'application/pdf',
      'Content-Disposition' => 'attachment; filename="facture_' . $order_id . '.pdf"',
    ]);
  }
}
