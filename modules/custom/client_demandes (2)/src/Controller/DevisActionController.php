<?php

namespace Drupal\client_demandes\Controller;

use <PERSON>upal\Core\Controller\ControllerBase;
use <PERSON>upal\Core\Mail\MailManagerInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use <PERSON><PERSON><PERSON>\node\NodeInterface;

class DevisActionController extends ControllerBase {

  /**
   * Accepte un devis.
   */
  public function dashboardAccept(NodeInterface $node) {
    if ($node->bundle() !== 'devis' || !$node->hasField('field_status') || !$node->hasField('field_demande_ref')) {
      $this->messenger()->addError($this->t('Ce contenu n’est pas un devis valide.'));
      return $this->redirect('<front>');
    }

    $current_uid = $this->currentUser()->id();
    $demande = $node->get('field_demande_ref')->entity ?? NULL;

    if (!$demande || $demande->getOwnerId() != $current_uid) {
      $this->messenger()->addError($this->t('Vous n’êtes pas autorisé à accepter ce devis.'));
      return $this->redirect('<front>');
    }

    // Mettre à jour le statut
    $node->set('field_status', 'accepted');
    $node->save();

    // Liens utiles
    $demande_link = $demande->toUrl('canonical', ['absolute' => TRUE])->toString();
    $devis_link = $node->toUrl('canonical', ['absolute' => TRUE])->toString();

    $mailManager = \Drupal::service('plugin.manager.mail');
    $langcode = \Drupal::currentUser()->getPreferredLangcode();

    // 📩 Email au prestataire
    $prestataire = $node->getOwner();
    $mailManager->mail('client_demandes', 'devis_accepted_notification', $prestataire->getEmail(), $prestataire->getPreferredLangcode(), [
      'name' => $prestataire->getDisplayName(),
      'demande_title' => $demande->label(),
      'demande_link' => $demande_link,
      'devis_link' => $devis_link,
    ]);

    // 📩 Email de confirmation au client
    $client = \Drupal::currentUser();
    $mailManager->mail('client_demandes', 'devis_accepted_confirmation', $client->getEmail(), $langcode, [
      'name' => $client->getDisplayName(),
      'demande_title' => $demande->label(),
      'demande_link' => $demande_link,
      'devis_link' => $devis_link,
    ]);

    $this->messenger()->addStatus($this->t('Le devis a été accepté. Les notifications ont été envoyées.'));
    return new RedirectResponse('/mon-espace/mes-demandes');
  }

  /**
   * Refuse un devis.
   */
  public function dashboardReject(NodeInterface $node) {
    if ($node->bundle() !== 'devis' || !$node->hasField('field_status') || !$node->hasField('field_demande_ref')) {
      $this->messenger()->addError($this->t('Ce contenu n’est pas un devis valide.'));
      return $this->redirect('<front>');
    }

    $current_uid = $this->currentUser()->id();
    $demande = $node->get('field_demande_ref')->entity ?? NULL;

    if (!$demande || $demande->getOwnerId() != $current_uid) {
      $this->messenger()->addError($this->t('Vous n’êtes pas autorisé à refuser ce devis.'));
      return $this->redirect('<front>');
    }

    $node->set('field_status', 'rejected');
    $node->save();

    $demande_link = $demande->toUrl('canonical', ['absolute' => TRUE])->toString();
    $devis_link = $node->toUrl('canonical', ['absolute' => TRUE])->toString();

    $mailManager = \Drupal::service('plugin.manager.mail');
    $langcode = \Drupal::currentUser()->getPreferredLangcode();

    $prestataire = $node->getOwner();
    $mailManager->mail('client_demandes', 'devis_rejected_notification', $prestataire->getEmail(), $prestataire->getPreferredLangcode(), [
      'name' => $prestataire->getDisplayName(),
      'demande_title' => $demande->label(),
      'demande_link' => $demande_link,
      'devis_link' => $devis_link,
    ]);

    $client = \Drupal::currentUser();
    $mailManager->mail('client_demandes', 'devis_rejected_confirmation', $client->getEmail(), $langcode, [
      'name' => $client->getDisplayName(),
      'demande_title' => $demande->label(),
      'demande_link' => $demande_link,
      'devis_link' => $devis_link,
    ]);

    $this->messenger()->addStatus($this->t('Le devis a été refusé. Les notifications ont été envoyées.'));
    return new RedirectResponse('/mon-espace/mes-demandes');
  }

}
