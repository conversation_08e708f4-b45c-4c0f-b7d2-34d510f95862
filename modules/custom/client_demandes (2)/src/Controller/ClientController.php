<?php

namespace Drupal\client_demandes\Controller;

use <PERSON><PERSON>al\Core\Controller\ControllerBase;
use <PERSON><PERSON><PERSON>\Core\Session\AccountInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Drupal\Core\Url;
use <PERSON><PERSON>al\node\Entity\Node;

/**
 * Contrôleur du tableau de bord client.
 */
class ClientController extends ControllerBase {

  /**
   * L'utilisateur courant.
   *
   * @var \Drupal\Core\Session\AccountInterface
   */
  protected $currentUser;

  /**
   * Constructeur.
   *
   * @param \Drupal\Core\Session\AccountInterface $current_user
   *   L'utilisateur actuellement connecté.
   */
  public function __construct(AccountInterface $current_user) {
    $this->currentUser = $current_user;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container) {
    return new static(
      $container->get('current_user')
    );
  }

  /**
   * Affiche le tableau de bord du client.
   *
   * @return array
   *   Un tableau de rendu.
   */
  public function dashboard() {
    // Récupérer les demandes créées par le client
    $demandes_ids = \Drupal::entityQuery('node')
      ->condition('type', 'demande')
      ->condition('uid', $this->currentUser->id())
      ->sort('created', 'DESC')
      ->accessCheck(TRUE)
      ->execute();
    $demandes = Node::loadMultiple($demandes_ids);

    // Récupérer les devis reçus pour ses demandes
    $devis_ids = \Drupal::entityQuery('node')
      ->condition('type', 'devis')
      ->condition('field_demande_ref.entity.uid', $this->currentUser->id())
      ->sort('created', 'DESC')
      ->accessCheck(TRUE)
      ->execute();
    $devis = Node::loadMultiple($devis_ids);

    // Ajouter les prestataires liés
foreach ($devis as $devis_node) {
  if ($devis_node->getOwner()) {
    $devis_node->prestataire_user = $devis_node->getOwner();
  }
}


    // Construire le rendu du tableau de bord
    return [
      '#theme' => 'client_dashboard',
      '#title' => $this->t('Tableau de bord client'),

      '#actions' => [
        '#type' => 'container',
        '#attributes' => ['class' => ['dashboard-actions']],
        'publier_demande' => [
          '#type' => 'link',
          '#title' => $this->t('📤 Publier une nouvelle demande'),
          '#url' => Url::fromRoute('client_demandes.demande_form'),
          '#attributes' => [
            'class' => ['button', 'button--primary'],
          ],
        ],
      ],

      '#demandes' => $demandes,
      '#devis_proposes' => $devis,
      '#attached' => [
        'library' => [
          'client_demandes/client_dashboard',
        ],
      ],
    ];
  }

}
