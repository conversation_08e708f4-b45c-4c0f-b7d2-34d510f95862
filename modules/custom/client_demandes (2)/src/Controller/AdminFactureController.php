<?php

namespace Drupal\client_demandes\Controller;

use <PERSON><PERSON>al\Core\Controller\ControllerBase;
use Symfony\Component\HttpFoundation\RedirectResponse;

class AdminFactureController extends ControllerBase {

  public function list() {
    $facture_dir = 'public://factures/';
    file_prepare_directory($facture_dir, FILE_CREATE_DIRECTORY | FILE_MODIFY_PERMISSIONS);
    $files = file_scan_directory($facture_dir, '/.*\.pdf$/');

    $rows = [];
    foreach ($files as $file) {
      $rows[] = [
        'data' => [
          ['data' => basename($file->uri)],
          ['data' => \Drupal::l('Télécharger', \Drupal\Core\Url::fromUri(file_create_url($file->uri)))],
        ],
      ];
    }

    return [
      '#type' => 'table',
      '#header' => ['Facture', 'Lien'],
      '#rows' => $rows,
      '#empty' => $this->t('Aucune facture générée.'),
    ];
  }
}
