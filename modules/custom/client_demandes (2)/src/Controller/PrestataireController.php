<?php

namespace Drupal\client_demandes\Controller;

use <PERSON><PERSON>al\Core\Controller\ControllerBase;
use <PERSON><PERSON><PERSON>\Core\Session\AccountInterface;
use Dr<PERSON>al\Core\Entity\EntityTypeManagerInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Drupal\commerce_order\Entity\Order;
use Drupal\node\Entity\Node;
use Drupal\user\Entity\User;

/**
 * Contrôleur pour le tableau de bord du prestataire.
 */
class PrestataireController extends ControllerBase {

  protected $entityTypeManager;
  protected $currentUser;

  public function __construct(EntityTypeManagerInterface $entity_type_manager, AccountInterface $current_user) {
    $this->entityTypeManager = $entity_type_manager;
    $this->currentUser = $current_user;
  }

  public static function create(ContainerInterface $container) {
    return new static(
      $container->get('entity_type.manager'),
      $container->get('current_user')
    );
  }

  /**
   * Page principale du tableau de bord prestataire.
   */
  public function dashboard() {
    $current_uid = $this->currentUser->id();

    $total_revenue = 0;
    $order_count = 0;

    $query = \Drupal::entityQuery('commerce_order')
      ->accessCheck(TRUE)
      ->sort('created', 'DESC')
      ->range(0, 50);
    $order_ids = $query->execute();

    foreach (Order::loadMultiple($order_ids) as $order) {
      foreach ($order->getItems() as $item) {
        $product = $item->getPurchasedEntity();
        if ($product && $product->getOwnerId() == $current_uid) {
          if ($order->getState()->value === 'completed') {
            $total_revenue += (float) $order->getTotalPrice()->getNumber();
            $order_count++;
          }
        }
      }
    }

    $summary = [
      '#theme' => 'item_list',
      '#title' => $this->t('Résumé'),
      '#items' => [
        'Nombre de commandes payées : ' . $order_count,
        'Chiffre d’affaires total : ' . number_format($total_revenue, 2) . ' €',
      ],
    ];

    $block = \Drupal::service('plugin.manager.block')->createInstance('paiement_notification_block', []);
    $block_build = $block->build();

    $demandes = $this->getDemandesPourPrestataire();

    return [
      '#theme' => 'prestataire_dashboard',
      '#demandes' => $demandes,
      'summary' => $summary,
      'notifications' => $block_build,
    ];
  }

  /**
   * Récupère les demandes compatibles avec les catégories métier du prestataire.
   */
  protected function getDemandesPourPrestataire() {
    $account = User::load($this->currentUser->id());

    if (!$account->hasField('field_categorie_metier') || $account->get('field_categorie_metier')->isEmpty()) {
      return [];
    }

    $categories_ids = [];
    foreach ($account->get('field_categorie_metier') as $term_ref) {
      $categories_ids[] = $term_ref->target_id;
    }

    $query = \Drupal::entityQuery('node')
      ->condition('type', 'demande')
      ->condition('status', 1)
      ->condition('field_categorie', $categories_ids, 'IN')
      ->sort('created', 'DESC')
      ->accessCheck(TRUE);

    $nids = $query->execute();
    return $this->entityTypeManager->getStorage('node')->loadMultiple($nids);
  }

  /**
   * Liste des devis envoyés par le prestataire connecté.
   */
  public function mySentOffers() {
    $uid = $this->currentUser->id();

    $query = $this->entityTypeManager->getStorage('node')->getQuery()
      ->condition('type', 'devis')
      ->condition('uid', $uid)
      ->sort('created', 'DESC')
      ->accessCheck(TRUE);

    $nids = $query->execute();
    $offers = $this->entityTypeManager->getStorage('node')->loadMultiple($nids);

    $rows = [];
    foreach ($offers as $offer) {
      $title = $offer->label();
      $created = \Drupal::service('date.formatter')->format($offer->getCreatedTime(), 'short');

      $client_name = '';
      if ($offer->hasField('field_client') && !$offer->get('field_client')->isEmpty()) {
        $client = $offer->get('field_client')->entity;
        $client_name = $client ? $client->getDisplayName() : '';
      }

      $rows[] = [
        'data' => [
          $title,
          $client_name,
          $created,
          $offer->toLink($this->t('Voir'))->toString(),
        ],
      ];
    }

    return [
      '#type' => 'table',
      '#header' => [$this->t('Titre'), $this->t('Client'), $this->t('Date'), $this->t('Lien')],
      '#rows' => $rows,
      '#empty' => $this->t('Aucune offre envoyée.'),
    ];
  }

  /**
   * Hook de personnalisation d'e-mail.
   */
  public function client_demandes_mail($key, &$message, $params) {
    switch ($key) {
      case 'facture_envoyee':
        $message['subject'] = $params['subject'];
        $message['body'][] = $params['body'];
        if (!empty($params['attachments'])) {
          $message['attachments'] = $params['attachments'];
        }
        break;
    }
  }

}
