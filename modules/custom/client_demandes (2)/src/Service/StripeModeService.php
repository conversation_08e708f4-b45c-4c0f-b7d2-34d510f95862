<?php

namespace Drupal\client_demandes\Service;

use Drupal\Core\Config\ConfigFactoryInterface;

/**
 * Service pour gérer le mode de paiement Stripe.
 */
class StripeModeService
{

  /**
   * Le service de configuration.
   *
   * @var \Drupal\Core\Config\ConfigFactoryInterface
   */
  protected $configFactory;

  /**
   * Constructeur.
   */
  public function __construct(ConfigFactoryInterface $config_factory)
  {
    $this->configFactory = $config_factory;
  }

  /**
   * Récupère le mode de paiement configuré.
   *
   * @return string
   *   Le mode de paiement ('complet' ou 'multi').
   */
  public function getPaymentMode()
  {
    $config = $this->configFactory->get('client_demandes.settings');
    return $config->get('mode_paiement') ?: 'complet';
  }
}
