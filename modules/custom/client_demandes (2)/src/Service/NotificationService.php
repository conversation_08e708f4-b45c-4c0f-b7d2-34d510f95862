<?php

namespace Drupal\client_demandes\Service;

use Drupal\Core\Entity\EntityTypeManagerInterface;
use Drupal\Core\Mail\MailManagerInterface;
use Drupal\Core\Language\LanguageManagerInterface;
use Drupal\Core\StringTranslation\StringTranslationTrait;
use <PERSON><PERSON>al\Core\Config\ConfigFactoryInterface;
use <PERSON><PERSON><PERSON>\node\NodeInterface;
use <PERSON><PERSON><PERSON>\user\UserInterface;

/**
 * Service pour gérer les notifications du module Client Demandes.
 */
class NotificationService {
  use StringTranslationTrait;

  /**
   * Le gestionnaire d'entités.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected $entityTypeManager;

  /**
   * Le gestionnaire de mail.
   *
   * @var \Drupal\Core\Mail\MailManagerInterface
   */
  protected $mailManager;

  /**
   * Le gestionnaire de langues.
   *
   * @var \Drupal\Core\Language\LanguageManagerInterface
   */
  protected $languageManager;

  /**
   * La factory de configuration.
   *
   * @var \Drupal\Core\Config\ConfigFactoryInterface
   */
  protected $configFactory;

  /**
   * Constructeur.
   *
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   Le gestionnaire d'entités.
   * @param \Drupal\Core\Mail\MailManagerInterface $mail_manager
   *   Le gestionnaire de mail.
   * @param \Drupal\Core\Language\LanguageManagerInterface $language_manager
   *   Le gestionnaire de langues.
   * @param \Drupal\Core\Config\ConfigFactoryInterface $config_factory
   *   La factory de configuration.
   */
  public function __construct(
    EntityTypeManagerInterface $entity_type_manager,
    MailManagerInterface $mail_manager,
    LanguageManagerInterface $language_manager,
    ConfigFactoryInterface $config_factory
  ) {
    $this->entityTypeManager = $entity_type_manager;
    $this->mailManager = $mail_manager;
    $this->languageManager = $language_manager;
    $this->configFactory = $config_factory;
  }

  /**
   * Notifie un client qu'un nouveau devis a été proposé.
   *
   * @param \Drupal\node\NodeInterface $devis
   *   Le nœud du devis.
   * @param \Drupal\node\NodeInterface $demande
   *   Le nœud de la demande.
   * @param \Drupal\user\UserInterface $client
   *   L'utilisateur client à notifier.
   */
  public function notifyNewDevis(NodeInterface $devis, NodeInterface $demande, UserInterface $client) {
    // Créer un message pour le client
    $message = $this->entityTypeManager->getStorage('message')->create([
      'template' => 'new_devis',
      'uid' => $client->id(),
      'field_node_reference' => ['target_id' => $devis->id()],
      'field_read' => 0,
    ]);
    $message->save();
    
    // Envoyer un email si la configuration le permet
    if ($this->configFactory->get('client_demandes.settings')->get('send_email_notifications')) {
      $langcode = $this->languageManager->getDefaultLanguage()->getId();
      $params = [
        'name' => $client->getDisplayName(),
        'demande_title' => $demande->getTitle(),
        'devis_id' => $devis->id(),
      ];
      
      $this->mailManager->mail(
        'client_demandes',
        'new_devis_notification',
        $client->getEmail(),
        $langcode,
        $params
      );
    }
  }

  /**
   * Notifie un prestataire que son devis a été accepté.
   *
   * @param \Drupal\node\NodeInterface $devis
   *   Le nœud du devis.
   * @param \Drupal\node\NodeInterface $demande
   *   Le nœud de la demande.
   * @param \Drupal\user\UserInterface $prestataire
   *   L'utilisateur prestataire à notifier.
   */
  public function notifyDevisAccepted(NodeInterface $devis, NodeInterface $demande, UserInterface $prestataire) {
    // Créer un message pour le prestataire
    $message = $this->entityTypeManager->getStorage('message')->create([
      'template' => 'devis_accepted',
      'uid' => $prestataire->id(),
      'field_node_reference' => ['target_id' => $devis->id()],
      'field_read' => 0,
    ]);
    $message->save();
    
    // Envoyer un email si la configuration le permet
    if ($this->configFactory->get('client_demandes.settings')->get('send_email_notifications')) {
      $langcode = $this->languageManager->getDefaultLanguage()->getId();
      $params = [
        'name' => $prestataire->getDisplayName(),
        'demande_title' => $demande->getTitle(),
        'devis_id' => $devis->id(),
      ];
      
      $this->mailManager->mail(
        'client_demandes',
        'devis_accepted_notification',
        $prestataire->getEmail(),
        $langcode,
        $params
      );
    }
  }

  /**
   * Notifie un prestataire que son devis a été refusé.
   *
   * @param \Drupal\node\NodeInterface $devis
   *   Le nœud du devis.
   * @param \Drupal\node\NodeInterface $demande
   *   Le nœud de la demande.
   * @param \Drupal\user\UserInterface $prestataire
   *   L'utilisateur prestataire à notifier.
   */
  public function notifyDevisRejected(NodeInterface $devis, NodeInterface $demande, UserInterface $prestataire) {
    // Créer un message pour le prestataire
    $message = $this->entityTypeManager->getStorage('message')->create([
      'template' => 'devis_rejected',
      'uid' => $prestataire->id(),
      'field_node_reference' => ['target_id' => $devis->id()],
      'field_read' => 0,
    ]);
    $message->save();
    
    // Envoyer un email si la configuration le permet
    if ($this->configFactory->get('client_demandes.settings')->get('send_email_notifications')) {
      $langcode = $this->languageManager->getDefaultLanguage()->getId();
      $params = [
        'name' => $prestataire->getDisplayName(),
        'demande_title' => $demande->getTitle(),
        'devis_id' => $devis->id(),
      ];
      
      $this->mailManager->mail(
        'client_demandes',
        'devis_rejected_notification',
        $prestataire->getEmail(),
        $langcode,
        $params
      );
    }
  }

}