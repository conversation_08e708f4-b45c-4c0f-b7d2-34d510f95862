<?php

namespace Drupal\client_demandes\Form;

use Drupal\Core\Form\ConfigFormBase;
use <PERSON>upal\Core\Form\FormStateInterface;

/**
 * Configure global defaults for client demandes.
 */
class ClientDemandesSettingsForm extends ConfigFormBase {

  /**
   * {@inheritdoc}
   */
  protected function getEditableConfigNames() {
    return ['client_demandes.settings'];
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'client_demandes_settings_form';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    $config = $this->config('client_demandes.settings');

    $form['default_payment_mode'] = [
      '#type' => 'radios',
      '#title' => $this->t('Mode de paiement par défaut (si non spécifié par le prestataire)'),
      '#options' => [
        'complet' => $this->t('Paiement complet'),
        'multi' => $this->t('Paiement partagé (plateforme + prestataire)'),
      ],
      '#default_value' => $config->get('default_payment_mode') ?: 'complet',
    ];

    return parent::buildForm($form, $form_state);
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    $this->config('client_demandes.settings')
      ->set('default_payment_mode', $form_state->getValue('default_payment_mode'))
      ->save();

    parent::submitForm($form, $form_state);
  }
}
