<?php

namespace Drupal\client_demandes\Form;

use Drupal\Core\Form\FormBase;
use Drupal\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\user\Entity\User;
use Drupal\Core\Config\ConfigFactoryInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

class PrestatairePreferencesForm extends FormBase {

  /** @var \Drupal\Core\Config\ConfigFactoryInterface */
  protected $configFactory;

  public function __construct(ConfigFactoryInterface $config_factory) {
    $this->configFactory = $config_factory;
  }

  public static function create(ContainerInterface $container) {
    return new static(
      $container->get('config.factory')
    );
  }

  public function getFormId() {
    return 'prestataire_preferences_form';
  }

  public function buildForm(array $form, FormStateInterface $form_state) {
    $user = \Drupal::currentUser();
    $account = User::load($user->id());

    $default_config = $this->configFactory->get('client_demandes.settings');
    $default_mode = $default_config->get('default_payment_mode') ?? 'complet';

    $value = $account->get('field_payment_mode_preference')->value;
    $default_value = $value ?: $default_mode;

    $form['payment_mode'] = [
      '#type' => 'radios',
      '#title' => $this->t('Préférence de paiement'),
      '#options' => [
        'complet' => $this->t('Paiement direct (100%)'),
        'multi' => $this->t('Paiement partagé (plateforme + prestataire)'),
      ],
      '#default_value' => $default_value,
    ];

    $form['submit'] = [
      '#type' => 'submit',
      '#value' => $this->t('Enregistrer'),
    ];

    return $form;
  }

  public function submitForm(array &$form, FormStateInterface $form_state) {
    $user = \Drupal::currentUser();
    $account = User::load($user->id());
    $account->set('field_payment_mode_preference', $form_state->getValue('payment_mode'));
    $account->save();

    \Drupal::messenger()->addStatus($this->t('Préférence enregistrée.'));
  }
}
