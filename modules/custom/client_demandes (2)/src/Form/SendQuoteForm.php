<?php

namespace Drupal\client_demandes\Form;

use <PERSON>upal\Core\Form\FormBase;
use <PERSON>upal\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\node\Entity\Node;
use Drupal\commerce_order\Entity\Order;
use Drupal\commerce_order\Entity\OrderItem;
use Drupal\commerce_price\Price;
use Drupal\Core\Session\AccountInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Drupal\Core\Entity\EntityTypeManagerInterface;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;

/**
 * Formulaire pour proposer un devis sur une demande.
 */
class SendQuoteForm extends FormBase {

  protected $currentUser;
  protected $entityTypeManager;

  public function __construct(AccountInterface $current_user, EntityTypeManagerInterface $entity_type_manager) {
    $this->currentUser = $current_user;
    $this->entityTypeManager = $entity_type_manager;
  }

  public static function create(ContainerInterface $container) {
    return new static(
      $container->get('current_user'),
      $container->get('entity_type.manager')
    );
  }

  public function getFormId() {
    return 'client_demandes_send_quote_form';
  }

  public function buildForm(array $form, FormStateInterface $form_state, Node $node = NULL) {
    if (!$node || $node->bundle() !== 'demande') {
      throw new AccessDeniedHttpException();
    }

    $form['quote_amount'] = [
      '#type' => 'number',
      '#title' => $this->t('Montant du devis (€)'),
      '#required' => TRUE,
      '#step' => 0.01,
    ];

    $form['quote_description'] = [
      '#type' => 'textarea',
      '#title' => $this->t('Description du devis'),
      '#required' => TRUE,
    ];

    $form['mode_paiement'] = [
      '#type' => 'radios',
      '#title' => $this->t('Mode de paiement souhaité'),
      '#options' => [
        'complet' => $this->t('Paiement complet au prestataire'),
        'multi' => $this->t('Paiement partagé (plateforme + prestataire)'),
      ],
      '#default_value' => 'complet',
      '#required' => TRUE,
    ];

    $form['node_id'] = [
      '#type' => 'hidden',
      '#value' => $node->id(),
    ];

    $form['submit'] = [
      '#type' => 'submit',
      '#value' => $this->t('Envoyer le devis'),
    ];

    return $form;
  }

  public function submitForm(array &$form, FormStateInterface $form_state) {
    $node_id = $form_state->getValue('node_id');
    $amount = $form_state->getValue('quote_amount');
    $description = $form_state->getValue('quote_description');
    $mode = $form_state->getValue('mode_paiement');
    $node = Node::load($node_id);
    $client_uid = $node->getOwnerId();

    $devis = Node::create([
      'type' => 'devis',
      'title' => 'Devis proposé',
      'field_prix' => $amount,
      'field_description' => $description,
      'field_mode_paiement' => $mode,
      'field_demande_ref' => ['target_id' => $node_id],
      'uid' => $this->currentUser->id(),
      'status' => 1,
    ]);
    $devis->save();

    // Créer un item de commande
    $order_item = OrderItem::create([
      'type' => 'default',
      'purchased_entity' => NULL,
      'title' => 'Prestation : ' . $node->label(),
      'quantity' => 1,
      'unit_price' => new Price($amount, 'EUR'),
    ]);
    $order_item->save();

    // Créer la commande
    $order = Order::create([
      'type' => 'default',
      'uid' => $client_uid,
      'state' => 'draft',
      'order_items' => [$order_item],
      'field_devis_ref' => ['target_id' => $devis->id()],
      'field_mode_paiement' => $mode,
    ]);
    $order->save();

    \Drupal::messenger()->addMessage($this->t('Le devis a été envoyé et une commande a été créée.'));
  }
}
