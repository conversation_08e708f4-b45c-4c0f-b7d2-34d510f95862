<?php

namespace Drupal\client_demandes\Form;

use Drupal\Core\Form\ConfigFormBase;
use <PERSON>upal\Core\Form\FormStateInterface;

/**
 * Configure les options Stripe par défaut.
 */
class StripeSettingsForm extends ConfigFormBase {

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'client_demandes_stripe_settings';
  }

  /**
   * {@inheritdoc}
   */
  protected function getEditableConfigNames() {
    return ['client_demandes.stripe_settings'];
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    $config = $this->config('client_demandes.stripe_settings');

    $form['commission_percentage'] = [
      '#type' => 'number',
      '#title' => $this->t('Commission de la plateforme (%)'),
      '#description' => $this->t('Pourcentage prélevé par la plateforme sur chaque paiement partagé.'),
      '#default_value' => $config->get('commission_percentage') ?? 10,
      '#min' => 0,
      '#step' => 1,
    ];

    $form['default_payment_mode'] = [
      '#type' => 'radios',
      '#title' => $this->t('Mode de paiement par défaut'),
      '#options' => [
        'complet' => $this->t('Paiement complet au prestataire'),
        'multi' => $this->t('Paiement partagé (plateforme + prestataire)'),
      ],
      '#default_value' => $config->get('default_payment_mode') ?? 'complet',
    ];

    return parent::buildForm($form, $form_state);
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    $this->configFactory->getEditable('client_demandes.stripe_settings')
      ->set('commission_percentage', $form_state->getValue('commission_percentage'))
      ->set('default_payment_mode', $form_state->getValue('default_payment_mode'))
      ->save();

    parent::submitForm($form, $form_state);
  }

} 
