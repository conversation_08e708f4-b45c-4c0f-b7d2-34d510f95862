<?php

namespace Drupal\client_demandes\Form;

use <PERSON><PERSON>al\Core\Form\ConfirmFormBase;
use <PERSON>upal\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\Core\Url;
use <PERSON><PERSON>al\node\Entity\Node;
use <PERSON><PERSON>al\Core\Session\AccountInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Drupal\Core\Entity\EntityTypeManagerInterface;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;

/**
 * Formulaire pour accepter ou refuser un devis.
 */
class DevisActionForm extends ConfirmFormBase {

  /**
   * L'utilisateur courant.
   *
   * @var \Drupal\Core\Session\AccountInterface
   */
  protected $currentUser;

  /**
   * Le gestionnaire d'entités.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected $entityTypeManager;

  /**
   * Le nœud du devis.
   *
   * @var \Drupal\node\NodeInterface
   */
  protected $node;

  /**
   * L'action à effectuer (accept ou reject).
   *
   * @var string
   */
  protected $action;

  /**
   * Constructeur.
   *
   * @param \Drupal\Core\Session\AccountInterface $current_user
   *   L'utilisateur courant.
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   Le gestionnaire d'entités.
   */
  public function __construct(AccountInterface $current_user, EntityTypeManagerInterface $entity_type_manager) {
    $this->currentUser = $current_user;
    $this->entityTypeManager = $entity_type_manager;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container) {
    return new static(
      $container->get('current_user'),
      $container->get('entity_type.manager')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'client_demandes_devis_action_form';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state, Node $node = NULL, $action = NULL) {
    $this->node = $node;
    $this->action = $action;

    // Vérifier que le nœud est un devis
    if ($node->getType() != 'devis') {
      throw new AccessDeniedHttpException();
    }

    // Vérifier que l'utilisateur est le propriétaire de la demande associée
    if ($node->hasField('field_demande_ref') && !$node->get('field_demande_ref')->isEmpty()) {
      $demande_id = $node->get('field_demande_ref')->target_id;
      $demande = Node::load($demande_id);
      if ($demande && $demande->getOwnerId() != $this->currentUser->id()) {
        throw new AccessDeniedHttpException();
      }
    } else {
      throw new AccessDeniedHttpException();
    }

    // Vérifier que le devis est en attente
    if ($node->hasField('field_status') && $node->get('field_status')->value != 'pending') {
      $this->messenger()->addWarning($this->t('Ce devis a déjà été traité.'));
      return $this->redirect('client_demandes.client_dashboard');
    }

    return parent::buildForm($form, $form_state);
  }

  /**
   * {@inheritdoc}
   */
  public function getQuestion() {
    if ($this->action == 'accept') {
      return $this->t('Êtes-vous sûr de vouloir accepter ce devis ?');
    } else {
      return $this->t('Êtes-vous sûr de vouloir refuser ce devis ?');
    }
  }

  /**
   * {@inheritdoc}
   */
  public function getDescription() {
    if ($this->action == 'accept') {
      return $this->t('En acceptant ce devis, vous vous engagez à payer le montant indiqué. Une commande sera créée automatiquement.');
    } else {
      return $this->t('Le prestataire sera informé que vous avez refusé son devis.');
    }
  }

  /**
   * {@inheritdoc}
   */
  public function getCancelUrl() {
    return new Url('entity.node.canonical', ['node' => $this->node->id()]);
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    // Mettre à jour le statut du devis
    $status = ($this->action == 'accept') ? 'accepted' : 'rejected';
    $this->node->set('field_status', $status);
    $this->node->save();

    // Notifier le prestataire
    $this->notifyPrestataire($status);

    // Si le devis est accepté, mettre à jour le statut de la demande
    if ($status == 'accepted') {
      $demande_id = $this->node->get('field_demande_ref')->target_id;
      $demande = Node::load($demande_id);
      if ($demande && $demande->hasField('field_statut_demande')) {
        $demande->set('field_statut_demande', 'en_cours');
        $demande->save();
      }
    }

    // Message de confirmation
    if ($status == 'accepted') {
      $this->messenger()->addStatus($this->t('Le devis a été accepté. Une commande a été créée.'));
    } else {
      $this->messenger()->addStatus($this->t('Le devis a été refusé.'));
    }

    // Rediriger vers le tableau de bord
    $form_state->setRedirect('client_demandes.client_dashboard');
  }

  /**
   * Notifie le prestataire de l'action sur son devis.
   *
   * @param string $status
   *   Le nouveau statut du devis.
   */
  protected function notifyPrestataire($status) {
    // Récupérer le prestataire (propriétaire du devis)
    $prestataire_id = $this->node->getOwnerId();

    // Créer un message pour le prestataire
    $template = ($status == 'accepted') ? 'devis_accepted' : 'devis_rejected';
    $message = \Drupal\message\Entity\Message::create([
      'template' => $template,
      'uid' => $prestataire_id,
      'field_node_reference' => ['target_id' => $this->node->id()],
      'field_read' => 0,
    ]);
    $message->save();
  }

}