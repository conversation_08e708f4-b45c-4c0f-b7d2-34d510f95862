{#
  Template : prestataire_dashboard.html.twig
  Affiche les demandes compatibles pour un prestataire.
#}

<div class="layout-content">
  <div class="container">
    <h1>{{ 'Tableau de bord prestataire'|t }}</h1>

    <div class="dashboard-summary">
      {{ summary }}
    </div>

    <div class="dashboard-notifications">
      {{ notifications }}
    </div>

    <h2>{{ 'Demandes compatibles avec votre domaine'|t }}</h2>

    {% if demandes|length %}
      <div class="demandes-list">
        {% for demande in demandes %}
          <div class="demande-card">
            <h3>{{ demande.label }}</h3>

            <div class="demande-meta">
              <p><strong>{{ 'Publié le'|t }}:</strong> {{ demande.created.value|date('d/m/Y') }}</p>
              {% if demande.hasField('field_localisation') %}
                <p><strong>{{ 'Localisation'|t }}:</strong> {{ demande.get('field_localisation').value }}</p>
              {% endif %}
              {% if demande.hasField('field_budget') %}
                <p><strong>{{ 'Budget'|t }}:</strong> {{ demande.get('field_budget').value }} €</p>
              {% endif %}
              {% if demande.hasField('field_delai') %}
                <p><strong>{{ 'Délai'|t }}:</strong> {{ demande.get('field_delai').value }}</p>
              {% endif %}
            </div>

            <div class="demande-actions">
              <a href="{{ path('entity.node.canonical', {'node': demande.id}) }}" class="button">
                {{ 'Voir la demande'|t }}
              </a>
              <a href="{{ path('client_demandes.send_quote_form', {'node': demande.id}) }}" class="button button--primary">
                📩 {{ 'Faire une proposition'|t }}
              </a>
            </div>
          </div>
        {% endfor %}
      </div>
    {% else %}
      <p>{{ 'Aucune demande correspondante trouvée.'|t }}</p>
    {% endif %}
  </div>
</div>