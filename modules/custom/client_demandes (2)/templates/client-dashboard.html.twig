{#
/**
 * @file
 * Template pour le tableau de bord client.
 *
 * Variables disponibles:
 * - title: Le titre du tableau de bord.
 * - demandes: <PERSON> demandes du client.
 * - devis_proposes: Les devis proposés au client.
 * - messages: Les messages non lus.
 * - actions: Boutons d'action (ex. publier une demande)
 */
#}

<div class="layout-content">
  <div class="container">
    <div class="region region-content">
      <div class="block block-system">
        <div class="content">

          <h1>{{ title }}</h1>

          {% if actions is defined %}
            <div class="dashboard-actions">
              {% for action in actions %}
                {{ action }}
              {% endfor %}
            </div>
          {% endif %}

          <h2>{{ 'Devis proposés'|t }}</h2>
          <div class="devis-list">
            {% for d in devis_proposes %}

              <div class="devis-card">

                <h3>{{ d.label }}</h3>

                {% set prestataire = d.prestataire_user ?? null %}
{% if prestataire %}
  <div class="devis-prestataire">
    <div class="prestataire-avatar">
      {% if prestataire.user_picture.entity %}
        <img src="{{ file_url(prestataire.user_picture.entity.uri.value) }}" alt="{{ prestataire.getDisplayName() }}" />
      {% else %}
        <img src="{{ base_path ~ directory }}/images/avatar-default.png" alt="avatar" />
      {% endif %}
    </div>
    <div class="prestataire-name">
      <a href="{{ path('entity.user.canonical', {'user': prestataire.id}) }}">
        {{ prestataire.getDisplayName() }}
      </a>
    </div>
  </div>
{% endif %}

                <div class="devis-meta">
                  <div class="devis-date">{{ d.created.value|date('d/m/Y') }}</div>
                  {% if d.hasField('field_status') and d.get('field_status').value is not empty %}
                    <div class="devis-status {{ d.get('field_status').value }}">
                      {% set status = d.get('field_status').value %}
                      {% if status is iterable %}
                        {% set status = status|first %}
                      {% endif %}
                      {{ status|capitalize }}
                    </div>
                  {% endif %}
                </div>
                <div class="devis-actions">
                  <a href="{{ path('entity.node.canonical', {'node': d.id}) }}" class="button">{{ 'Voir détails'|t }}</a>
                  {% if d.hasField('field_status') and d.get('field_status').value is not empty %}
                    {% set status = d.get('field_status').value %}
                    {% if status is iterable %}
                      {% set status = status|first %}
                    {% endif %}
                    {% if status == 'pending' %}
                      <a href="{{ path('client_demandes.dashboard_accept_devis', {'node': d.id}) }}" class="button button--primary">{{ 'Accepter'|t }}</a>
                      <a href="{{ path('client_demandes.dashboard_reject_devis', {'node': d.id}) }}" class="button button--danger">{{ 'Refuser'|t }}</a>
                    {% endif %}
                  {% endif %}
                </div>
              </div>
            {% else %}
              <p>{{ 'Aucun devis proposé pour le moment.'|t }}</p>
            {% endfor %}
          </div>

          <h2>{{ 'Mes demandes'|t }}</h2>
          <div class="demandes-list">
            {% for demande in demandes %}
              <div class="demande-card">
                <h3>{{ demande.label }}</h3>
                <div class="demande-meta">
                  <div class="demande-date">{{ demande.created.value|date('d/m/Y') }}</div>
                  {% if demande.hasField('field_statut_demande') and demande.get('field_statut_demande').value is not empty %}
                    {% set statut = demande.get('field_statut_demande').value %}
                    {% if statut is iterable %}
                      {% set statut = statut|first %}
                    {% endif %}
                    <div class="demande-status {{ statut }}">
                      {{ statut|replace({'_': ' '})|capitalize }}
                    </div>
                  {% endif %}
                </div>
                <div class="demande-actions">
                  <a href="{{ path('entity.node.canonical', {'node': demande.id}) }}" class="button">{{ 'Voir détails'|t }}</a>
                </div>
              </div>
            {% else %}
              <p>{{ 'Vous n\'avez pas encore publié de demande.'|t }}</p>
            {% endfor %}
          </div>

        </div>
      </div>
    </div>
  </div>
</div>
