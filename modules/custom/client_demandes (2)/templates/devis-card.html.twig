{#
/**
 * @file
 * Template pour une carte de devis.
 *
 * Variables disponibles:
 * - devis: Le nœud du devis.
 */
#}

<div class="devis-card">
	<h3>{{ devis.label }}</h3>

	{% if devis.hasField('field_demande_ref') and not devis.get('field_demande_ref').isEmpty() %}
		{% set demande = devis.get('field_demande_ref').entity %}
		<div class="devis-demande">
			<a href="{{ path('entity.node.canonical', {'node': demande.id}) }}">{{ demande.label }}</a>
		</div>
	{% endif %}

	{% if devis.hasField('field_price') and not devis.get('field_price').isEmpty() %}
		<div class="devis-price">
			<i class="fas fa-euro-sign"></i>
			{{ devis.get('field_price').value }}
			€
		</div>
	{% endif %}

	{% if devis.hasField('field_delay') and not devis.get('field_delay').isEmpty() %}
		<div class="devis-delay">
			<i class="fas fa-clock"></i>
			{{ devis.get('field_delay').value }}
		</div>
	{% endif %}

	{% if devis.hasField('field_status') and not devis.get('field_status').isEmpty() %}
		<div class="devis-status status-{{ devis.get('field_status').value }}">
			{% if devis.get('field_status').value == 'pending' %}
				{{ 'En attente'|t }}
			{% elseif devis.get('field_status').value == 'accepted' %}
				{{ 'Accepté'|t }}
			{% elseif devis.get('field_status').value == 'rejected' %}
				{{ 'Refusé'|t }}
			{% endif %}
		</div>
	{% endif %}

	<div class="devis-date">
		{{ 'Créé le'|t }}
		{{ devis.created.value|date('d/m/Y') }}
	</div>

	<div class="devis-actions">
		<a href="{{ path('entity.node.canonical', {'node': devis.id}) }}" class="button">{{ 'Voir détails'|t }}</a>
		{% if is_client and devis.hasField('field_status') and devis.get('field_status').value == 'pending' %}
			<a href="{{ path('client_demandes.dashboard_accept_devis', {'node': devis.id}) }}" class="button button--primary">{{ 'Accepter'|t }}</a>
			<a href="{{ path('client_demandes.dashboard_reject_devis', {'node': devis.id}) }}" class="button button--danger">{{ 'Refuser'|t }}</a>
		{% endif %}
	</div>
	{% if is_client and devis.field_status.value == 'pending' %}
  <div class="devis-actions">
    <a href="{{ path('client_demandes.dashboard_accept_devis', {'node': devis.id}) }}" class="button button--success">
      ✔ Accepter
    </a>
    <a href="{{ path('client_demandes.dashboard_reject_devis', {'node': devis.id}) }}" class="button button--danger">
      ✖ Refuser
    </a>
  </div>
{% endif %}

</div>
