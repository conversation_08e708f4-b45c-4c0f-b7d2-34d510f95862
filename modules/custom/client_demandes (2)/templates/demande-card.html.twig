{#
/**
 * @file
 * Template pour une carte de demande.
 *
 * Variables disponibles:
 * - demande: Le nœud de la demande.
 */
#}

<div class="demande-card">
  <h3><a href="{{ path('entity.node.canonical', {'node': demande.id}) }}">{{ demande.label }}</a></h3>
  
  {% if demande.hasField('field_categorie') and not demande.get('field_categorie').isEmpty() %}
    <div class="demande-categorie">
      {{ demande.get('field_categorie').entity.label }}
    </div>
  {% endif %}
  
  {% if demande.hasField('field_statut_demande') and not demande.get('field_statut_demande').isEmpty() %}
    <div class="demande-status status-{{ demande.get('field_statut_demande').value }}">
      {% if demande.get('field_statut_demande').value == 'ouverte' %}
        {{ 'Ouverte'|t }}
      {% elseif demande.get('field_statut_demande').value == 'en_cours' %}
        {{ 'En cours'|t }}
      {% elseif demande.get('field_statut_demande').value == 'terminee' %}
        {{ 'Terminée'|t }}
      {% elseif demande.get('field_statut_demande').value == 'annulee' %}
        {{ 'Annulée'|t }}
      {% endif %}
    </div>
  {% endif %}
  
  {% if demande.hasField('field_localisation') and not demande.get('field_localisation').isEmpty() %}
    <div class="demande-localisation">
      <i class="fas fa-map-marker-alt"></i> {{ demande.get('field_localisation').value }}
    </div>
  {% endif %}
  
  {% if demande.hasField('field_budget') and not demande.get('field_budget').isEmpty() %}
    <div class="demande-budget">
      <i class="fas fa-euro-sign"></i> {{ demande.get('field_budget').value }} €
    </div>
  {% endif %}
  
  {% if demande.hasField('field_delai') and not demande.get('field_delai').isEmpty() %}
    <div class="demande-delai">
      <i class="fas fa-clock"></i> {{ demande.get('field_delai').value }}
    </div>
  {% endif %}
  
  <div class="demande-date">
    {{ 'Publiée le'|t }} {{ demande.created.value|date('d/m/Y') }}
  </div>
  
  <div class="demande-actions">
    <a href="{{ path('entity.node.canonical', {'node': demande.id}) }}" class="button">{{ 'Voir détails'|t }}</a>
    {% if can_propose_devis %}
      <a href="{{ path('client_demandes.send_quote_form', {'node': demande.id}) }}" class="button button--primary">{{ 'Proposer un devis'|t }}</a>
    {% endif %}
  </div>
</div>