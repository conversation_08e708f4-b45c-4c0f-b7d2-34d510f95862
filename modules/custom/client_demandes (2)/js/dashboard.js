/**
 * @file
 * JavaScript pour les tableaux de bord client et prestataire.
 */

(function ($, <PERSON><PERSON><PERSON>) {
  'use strict';

  Drupal.behaviors.clientDemandesDashboard = {
    attach: function (context, settings) {
      // Ajouter des interactions si nécessaire
      $('.message-item', context).once('message-read').each(function () {
        $(this).on('click', '.button', function () {
          // Marquer le message comme lu lors du clic sur le bouton
          $(this).closest('.message-item').addClass('message-read');
        });
      });
    }
  };

})(j<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>);