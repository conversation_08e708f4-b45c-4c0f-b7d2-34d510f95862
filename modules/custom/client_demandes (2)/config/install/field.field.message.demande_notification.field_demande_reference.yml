langcode: fr
status: true
dependencies:
  config:
    - field.storage.message.field_demande_reference
    - message.template.demande_notification
    - node.type.demande
id: message.demande_notification.field_demande_reference
field_name: field_demande_reference
entity_type: message
bundle: demande_notification
label: 'Réf<PERSON>rence demande'
description: ''
required: true
translatable: false
default_value: {  }
field_type: entity_reference
settings:
  handler: 'default:node'
  handler_settings:
    target_bundles:
      demande: demande