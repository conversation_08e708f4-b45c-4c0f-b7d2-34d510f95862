/* Styling compatible with Olivero theme */
.devis-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin: 1.5rem 0;
}

.devis-card {
  background: var(--color--gray-95);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.devis-meta {
  display: flex;
  justify-content: space-between;
  margin: 1rem 0;
  font-size: 0.875rem;
}

.devis-status {
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius);
  font-weight: bold;
}

.devis-status.pending {
  background: var(--color--yellow-50);
  color: var(--color--black);
}

.devis-status.accepted {
  background: var(--color--green-50);
  color: var(--color--white);
}

.devis-status.rejected {
  background: var(--color--red-50);
  color: var(--color--white);
}

.devis-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
  flex-wrap: wrap;
}

.button--danger {
  background-color: var(--color--red-50);
  border-color: var(--color--red-50);
  color: var(--color--white);
}

.button--danger:hover {
  background-color: var(--color--red-30);
  border-color: var(--color--red-30);
}

/* Styles pour les demandes */
.demandes-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin: 1.5rem 0;
}

.demande-card {
  background: var(--color--gray-95);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.demande-meta {
  display: flex;
  justify-content: space-between;
  margin: 1rem 0;
  font-size: 0.875rem;
}

.demande-status {
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius);
  font-weight: bold;
}

.demande-status.en_attente {
  background: var(--color--yellow-50);
  color: var(--color--black);
}

.demande-status.en_cours {
  background: var(--color--blue-50);
  color: var(--color--white);
}

.demande-status.terminee {
  background: var(--color--green-50);
  color: var(--color--white);
}

.demande-status.annulee {
  background: var(--color--red-50);
  color: var(--color--white);
}

.demande-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
}
