/**
 * Styles pour les tableaux de bord client et prestataire.
 */

/* <PERSON> gén<PERSON>ux */
.client-dashboard,
.prestataire-dashboard {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e0e0e0;
}

.dashboard-actions {
  display: flex;
  gap: 10px;
}

.dashboard-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 30px;
}

@media (min-width: 768px) {
  .dashboard-content {
    grid-template-columns: 1fr 1fr;
  }
}

/* Sections */
.demandes-section,
.devis-section,
.messages-section {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.demandes-section h2,
.devis-section h2,
.messages-section h2 {
  margin-top: 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #e0e0e0;
}

/* Cartes */
.demandes-list,
.devis-list {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
  margin-top: 20px;
}

.demande-card,
.devis-card {
  background-color: white;
  border-radius: 6px;
  padding: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.demande-card:hover,
.devis-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.demande-card h3,
.devis-card h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 1.2em;
}

.demande-card h3 a,
.devis-card h3 a {
  color: #2c3e50;
  text-decoration: none;
}

.demande-card h3 a:hover,
.devis-card h3 a:hover {
  color: #3498db;
}

/* Informations des cartes */
.demande-categorie,
.devis-demande {
  display: inline-block;
  background-color: #e0f7fa;
  color: #006064;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 0.9em;
  margin-bottom: 10px;
}

.demande-localisation,
.demande-budget,
.demande-delai,
.demande-date,
.devis-price,
.devis-delay,
.devis-date {
  margin-bottom: 8px;
  font-size: 0.95em;
  color: #555;
}

/* Statuts */
.demande-status,
.devis-status {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 0.9em;
  margin-bottom: 10px;
}

.status-ouverte,
.status-pending {
  background-color: #fff8e1;
  color: #ff8f00;
}

.status-en_cours,
.status-accepted {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.status-terminee {
  background-color: #e3f2fd;
  color: #1565c0;
}

.status-annulee,
.status-rejected {
  background-color: #ffebee;
  color: #c62828;
}

/* Actions */
.demande-actions,
.devis-actions {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}

/* Messages */
.messages-list {
  margin-top: 20px;
}

.message-item {
  background-color: white;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.message-content {
  margin-bottom: 10px;
}

.message-date {
  font-size: 0.85em;
  color: #777;
  margin-bottom: 10px;
}

.message-actions {
  display: flex;
  justify-content: flex-end;
}

/* Boutons */
.button {
  display: inline-block;
  padding: 8px 16px;
  background-color: #f0f0f0;
  color: #333;
  border-radius: 4px;
  text-decoration: none;
  font-size: 0.9em;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.button:hover {
  background-color: #e0e0e0;
}

.button--primary {
  background-color: #3498db;
  color: white;
}

.button--primary:hover {
  background-color: #2980b9;
}

.button--danger {
  background-color: #e74c3c;
  color: white;
}

.button--danger:hover {
  background-color: #c0392b;
}


.devis-prestataire {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.prestataire-avatar img {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  margin-right: 10px;
}
.prestataire-name a {
  font-weight: bold;
  text-decoration: none;
  color: #333;
}
