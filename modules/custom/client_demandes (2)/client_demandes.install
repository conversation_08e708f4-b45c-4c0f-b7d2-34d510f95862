<?php

/**
 * @file
 * Installation et mise à jour du module Client Demandes.
 */

/**
 * Implements hook_install().
 */
function client_demandes_install()
{
  // Créer les rôles s'ils n'existent pas déjà
  $roles = ['client', 'prestataire'];
  foreach ($roles as $role_id) {
    $role = \Drupal\user\Entity\Role::load($role_id);
    if (!$role) {
      $role = \Drupal\user\Entity\Role::create([
        'id' => $role_id,
        'label' => ucfirst($role_id),
      ]);
      $role->save();
    }
  }

  // Créer le vocabulaire de taxonomie pour les catégories si nécessaire
  $vocabulary = \Drupal::entityTypeManager()->getStorage('taxonomy_vocabulary')->load('categories_services');
  if (!$vocabulary) {
    $vocabulary = \Drupal\taxonomy\Entity\Vocabulary::create([
      'vid' => 'categories_services',
      'name' => 'Catégories de services',
      'description' => 'Catégories pour les demandes de services',
    ]);
    $vocabulary->save();

    // Ajouter quelques termes par défaut
    $terms = [
      'Développement web',
      'Design graphique',
      'Marketing digital',
      'Rédaction web',
      'Traduction',
      'Conseil',
      'Formation',
      'Autre',
    ];

    foreach ($terms as $term_name) {
      $term = \Drupal\taxonomy\Entity\Term::create([
        'vid' => 'categories_services',
        'name' => $term_name,
      ]);
      $term->save();
    }
  }

  // Créer les templates de message
  $templates = [
    'new_devis' => [
      'label' => 'Nouveau devis reçu',
      'text' => 'Vous avez reçu un nouveau devis pour votre demande.',
    ],
    'devis_accepted' => [
      'label' => 'Devis accepté',
      'text' => 'Votre devis a été accepté par le client.',
    ],
    'devis_rejected' => [
      'label' => 'Devis refusé',
      'text' => 'Votre devis a été refusé par le client.',
    ],
  ];

  foreach ($templates as $template_id => $template_data) {
    $message_template = \Drupal::entityTypeManager()->getStorage('message_template')->load($template_id);
    if (!$message_template) {
      $message_template = \Drupal\message\Entity\MessageTemplate::create([
        'template' => $template_id,
        'label' => $template_data['label'],
        'description' => $template_data['label'],
        'text' => [
          ['value' => $template_data['text'], 'format' => 'basic_html'],
        ],
      ]);
      $message_template->save();
    }
  }

  // Informer l'utilisateur
  \Drupal::messenger()->addStatus(t('Le module Client Demandes a été installé avec succès.'));
}

/**
 * Implements hook_uninstall().
 */
function client_demandes_uninstall()
{
  // Nettoyer les configurations
  \Drupal::configFactory()->getEditable('client_demandes.settings')->delete();

  // Note: nous ne supprimons pas les rôles, vocabulaires ou types de contenu
  // car ils pourraient être utilisés par d'autres modules
}
