<?php

namespace Drupal\context_fix\Plugin\Condition;

use <PERSON><PERSON><PERSON>\user\Plugin\Condition\UserRole;
use Drupal\Component\Plugin\Exception\ContextException;

/**
 * Provides a 'Safe User Role' condition that handles missing user context.
 *
 * @Condition(
 *   id = "safe_user_role",
 *   label = @Translation("Safe User Role"),
 *   context_definitions = {
 *     "user" = @ContextDefinition("entity:user", label = @Translation("User"), required = FALSE)
 *   }
 * )
 */
class SafeUserRole extends UserRole {

  /**
   * {@inheritdoc}
   */
  public function evaluate() {
    if (empty($this->configuration['roles']) && !$this->isNegated()) {
      return TRUE;
    }
    
    try {
      $user = $this->getContextValue('user');
      
      // Si l'utilisateur est NULL ou vide, retourner FALSE (pas de rôles)
      if (!$user || !$user->id()) {
        return FALSE;
      }
      
      return (bool) array_intersect($this->configuration['roles'], $user->getRoles());
    } catch (ContextException $e) {
      // Si le contexte n'est pas disponible, considérer comme utilisateur anonyme
      return FALSE;
    }
  }

  /**
   * {@inheritdoc}
   */
  public function summary() {
    // Use the role labels. They will be sanitized below.
    $roles = array_map(fn($role) => $role->label(), \Drupal\user\Entity\Role::loadMultiple());
    $roles = array_intersect_key($roles, $this->configuration['roles']);
    if (count($roles) > 1) {
      $roles = implode(', ', $roles);
    }
    else {
      $roles = reset($roles);
    }
    if (!empty($this->configuration['negate'])) {
      return $this->t('The user is not a member of @roles (safe)', ['@roles' => $roles]);
    }
    else {
      return $this->t('The user is a member of @roles (safe)', ['@roles' => $roles]);
    }
  }

}
