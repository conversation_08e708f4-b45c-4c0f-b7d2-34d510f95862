<?php

/**
 * @file
 * Contains context_fix.module.
 */

use <PERSON><PERSON>al\Core\Routing\RouteMatchInterface;

/**
 * Implements hook_help().
 */
function context_fix_help($route_name, RouteMatchInterface $route_match) {
  switch ($route_name) {
    case 'help.page.context_fix':
      $output = '';
      $output .= '<h3>' . t('About') . '</h3>';
      $output .= '<p>' . t('This module fixes context-related issues with user conditions by providing safe alternatives.') . '</p>';
      return $output;

    default:
  }
}

/**
 * Implements hook_block_view_alter().
 */
function context_fix_block_view_alter(array &$build, \Drupal\Core\Block\BlockPluginInterface $block) {
  // Cette fonction peut être utilisée pour intercepter les blocs problématiques
  // et les désactiver si nécessaire
}

/**
 * Implements hook_condition_info_alter().
 */
function context_fix_condition_info_alter(&$definitions) {
  // Optionnel: modifier les définitions de conditions si nécessaire
}

/**
 * Implements hook_module_implements_alter().
 */
function context_fix_module_implements_alter(&$implementations, $hook) {
  // S'assurer que notre module s'exécute en dernier pour certains hooks
  if ($hook == 'block_view_alter') {
    $group = $implementations['context_fix'];
    unset($implementations['context_fix']);
    $implementations['context_fix'] = $group;
  }
}
