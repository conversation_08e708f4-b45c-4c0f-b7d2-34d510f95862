client_dashboard:
  version: 1.x
  css:
    theme:
      css/client-dashboard.css: {}
  js:
    js/dashboard.js: {}
  dependencies:
    - core/jquery
    - core/drupal

acceptation_devis:
  version: 1.x
  js:
    js/acceptation_devis.js: {}
  dependencies:
    - core/drupal
    - core/jquery

prestataire_dashboard:
  version: 1.x
  css:
    theme:
      css/prestataire-dashboard.css: {}
  js:
    js/dashboard.js: {}
  dependencies:
    - core/jquery
    - core/drupal

contact_info:
  version: 1.x
  css:
    theme:
      css/contact-info.css: {}
  dependencies:
    - core/drupal

stripe_connect:
  version: 1.x
  css:
    theme:
      css/stripe-connect.css: {}
  js:
    js/stripe-connect.js: {}
  dependencies:
    - core/jquery
    - core/drupal

paiement_options:
  version: 1.x
  css:
    theme:
      css/paiement-options.css: {}
  dependencies:
    - core/drupal

shop:
  version: 1.x
  css:
    theme:
      css/shop.css: {}
  js:
    js/shop.js: {}
  dependencies:
    - core/jquery
    - core/drupal
