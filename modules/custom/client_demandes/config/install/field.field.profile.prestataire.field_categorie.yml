langcode: fr
status: true
dependencies:
  config:
    - field.storage.profile.field_categorie
    - profile.type.prestataire
    - taxonomy.vocabulary.categorie_metier
id: profile.prestataire.field_categorie
field_name: field_categorie
entity_type: profile
bundle: prestataire
label: 'Catégories de services'
description: 'Sélectionnez les catégories de services que vous proposez'
required: true
translatable: false
default_value: {  }
field_type: entity_reference
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      categorie_metier: categorie_metier
    sort:
      field: name
      direction: asc
    auto_create: false
