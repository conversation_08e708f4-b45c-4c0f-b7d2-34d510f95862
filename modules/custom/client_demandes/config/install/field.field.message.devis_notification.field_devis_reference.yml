langcode: fr
status: true
dependencies:
  config:
    - field.storage.message.field_devis_reference
    - message.template.devis_notification
    - node.type.devis
id: message.devis_notification.field_devis_reference
field_name: field_devis_reference
entity_type: message
bundle: devis_notification
label: 'Réf<PERSON>rence devis'
description: 'Le devis associé à cette notification'
required: true
translatable: false
default_value: {  }
field_type: entity_reference
settings:
  handler: 'default:node'
  handler_settings:
    target_bundles:
      devis: devis
    sort:
      field: _none
    auto_create: false
