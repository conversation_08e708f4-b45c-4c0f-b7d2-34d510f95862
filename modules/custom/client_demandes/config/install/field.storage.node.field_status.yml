langcode: fr
status: true
dependencies:
  module:
    - node
    - options
id: node.field_status
field_name: field_status
entity_type: node
type: list_string
settings:
  allowed_values:
    -
      value: pending
      label: 'En attente'
    -
      value: validation
      label: 'En validation'
    -
      value: accepted
      label: 'Acceptée'
    -
      value: refused
      label: 'Refusée'
    -
      value: completed
      label: '<PERSON><PERSON><PERSON><PERSON>'
  allowed_values_function: ''
module: options
locked: false
cardinality: 1
translatable: false
indexes: {  }
persist_with_no_fields: false
custom_storage: false
