langcode: fr
status: true
dependencies:
  config:
    - field.storage.node.field_price
    - node.type.demande
  module:
    - node
id: node.demande.field_price
field_name: field_price
entity_type: node
bundle: demande
label: 'Prix estimé'
description: 'Budget estimé pour cette demande'
required: true
translatable: false
default_value: {  }
field_type: decimal
settings:
  min: 0
  max: null
  prefix: ''
  suffix: ' €'
