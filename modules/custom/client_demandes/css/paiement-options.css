/* Styles pour la page de sélection du mode de paiement */

.paiement-options {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.paiement-options h1 {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 2rem;
}

.devis-summary {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.devis-summary h3 {
  color: #495057;
  margin-bottom: 1rem;
  border-bottom: 2px solid #007bff;
  padding-bottom: 0.5rem;
}

.devis-summary p {
  margin-bottom: 0.5rem;
  color: #6c757d;
}

.payment-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.payment-option {
  background: #ffffff;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.payment-option:hover {
  border-color: #007bff;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.payment-option h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.payment-option p {
  color: #6c757d;
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.payment-option .button {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.button--primary {
  background-color: #007bff;
  color: white;
}

.button--primary:hover {
  background-color: #0056b3;
  color: white;
}

.button--secondary {
  background-color: #6c757d;
  color: white;
}

.button--secondary:hover {
  background-color: #545b62;
  color: white;
}

.button--tertiary {
  background-color: transparent;
  color: #6c757d;
  border: 1px solid #6c757d;
}

.button--tertiary:hover {
  background-color: #6c757d;
  color: white;
}

.actions {
  text-align: center;
  padding-top: 1rem;
  border-top: 1px solid #dee2e6;
}

/* Responsive design */
@media (max-width: 768px) {
  .paiement-options {
    padding: 1rem;
  }
  
  .payment-options {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .payment-option {
    padding: 1.5rem;
  }
}

/* Animation pour les boutons */
.payment-option .button {
  position: relative;
  overflow: hidden;
}

.payment-option .button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.payment-option .button:hover::before {
  left: 100%;
}
