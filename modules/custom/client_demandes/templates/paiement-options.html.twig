{#
  Template : paiement-options.html.twig
  Contexte : #devis
#}

<div class="paiement-options">
	<div class="container">
		<h1>{{ 'Choisissez votre mode de paiement'|t }}</h1>

		{% if devis %}
			<div class="devis-summary">
				<h3>{{ 'Récapitulatif du devis'|t }}</h3>
				<p>
					<strong>{{ 'Devis'|t }}:</strong>
					{{ devis.label }}</p>
				{% if devis.field_price is defined and devis.field_price.value %}
					<p>
						<strong>{{ 'Montant'|t }}:</strong>
						{{ devis.field_price.value|number_format(2, ',', ' ') }}
						€</p>
				{% endif %}
				{% if devis.owner %}
					<p>
						<strong>{{ 'Prestataire'|t }}:</strong>
						{{ devis.owner.getDisplayName() }}</p>
				{% endif %}
			</div>
		{% endif %}

		<div class="payment-options">
			<div class="payment-option">
				<h3>💳
					{{ 'Paiement complet'|t }}</h3>
				<p>{{ 'Payez la totalité du montant directement au prestataire'|t }}</p>
				<a href="{{ path('client_demandes.paiement_complet', {}, {'query': {'devis_id': devis.id}}) }}" class="button button--primary">
					{{ 'Paiement complet'|t }}
				</a>
			</div>

			<div class="payment-option">
				<h3>🤝
					{{ 'Paiement partiel'|t }}</h3>
				<p>{{ 'Payez une partie maintenant, le reste à la livraison'|t }}</p>
				<a href="{{ path('client_demandes.paiement_partiel', {}, {'query': {'devis_id': devis.id}}) }}" class="button button--secondary">
					{{ 'Paiement partiel'|t }}
				</a>
			</div>
		</div>

		<div class="actions">
			<a href="{{ path('client_demandes.client_dashboard') }}" class="button button--tertiary">
				{{ 'Retour au tableau de bord'|t }}
			</a>
		</div>
	</div>
</div>
