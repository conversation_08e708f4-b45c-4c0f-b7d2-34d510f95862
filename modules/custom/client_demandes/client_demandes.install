<?php

/**
 * @file
 * Installation et mise à jour du module Client Demandes.
 */

/**
 * Implements hook_install().
 */
function client_demandes_install()
{
  // Créer les rôles s'ils n'existent pas déjà
  $roles = ['client', 'prestataire'];
  foreach ($roles as $role_id) {
    $role = \Drupal\user\Entity\Role::load($role_id);
    if (!$role) {
      $role = \Drupal\user\Entity\Role::create([
        'id' => $role_id,
        'label' => ucfirst($role_id),
      ]);
      $role->save();
    }
  }

  // Créer le vocabulaire de taxonomie pour les catégories si nécessaire
  $vocabulary = \Drupal::entityTypeManager()->getStorage('taxonomy_vocabulary')->load('categories_service');
  if (!$vocabulary) {
    $vocabulary = \Drupal\taxonomy\Entity\Vocabulary::create([
      'vid' => 'categories_service',
      'name' => 'Catégories de services',
      'description' => 'Catégories pour les demandes de services',
    ]);
    $vocabulary->save();

    // Ajouter quelques termes par défaut
    $terms = [
      'Développement web',
      'Design graphique',
      'Marketing digital',
      'Rédaction web',
      'Traduction',
      'Conseil',
      'Formation',
      'Autre',
    ];

    foreach ($terms as $term_name) {
      $term = \Drupal\taxonomy\Entity\Term::create([
        'vid' => 'categories_service',
        'name' => $term_name,
      ]);
      $term->save();
    }
  }

  // Créer les templates de message
  $templates = [
    'new_devis' => [
      'label' => 'Nouveau devis reçu',
      'text' => 'Vous avez reçu un nouveau devis pour votre demande.',
    ],
    'devis_accepted' => [
      'label' => 'Devis accepté',
      'text' => 'Votre devis a été accepté par le client.',
    ],
    'devis_rejected' => [
      'label' => 'Devis refusé',
      'text' => 'Votre devis a été refusé par le client.',
    ],
    'nouvelle_demande_prestataire' => [
      'label' => 'Nouvelle demande disponible',
      'text' => 'Une nouvelle demande correspondant à vos compétences a été publiée : @demande_title par @client_name. <a href="@demande_url">Voir la demande</a>',
    ],
    'paiement_complete_client' => [
      'label' => 'Paiement confirmé - Coordonnées prestataire',
      'text' => 'Votre paiement a été confirmé. Coordonnées du prestataire : @prestataire_name (@prestataire_email, @prestataire_phone)',
    ],
    'paiement_complete_prestataire' => [
      'label' => 'Paiement reçu - Coordonnées client',
      'text' => 'Paiement reçu pour @devis_title. Coordonnées du client : @client_name (@client_email, @client_phone). Montant : @amount €',
    ],
  ];

  foreach ($templates as $template_id => $template_data) {
    $message_template = \Drupal::entityTypeManager()->getStorage('message_template')->load($template_id);
    if (!$message_template) {
      $message_template = \Drupal\message\Entity\MessageTemplate::create([
        'template' => $template_id,
        'label' => $template_data['label'],
        'description' => $template_data['label'],
        'text' => [
          ['value' => $template_data['text'], 'format' => 'basic_html'],
        ],
      ]);
      $message_template->save();
    }
  }

  // Créer les champs du profil prestataire s'ils n'existent pas
  _client_demandes_create_profile_fields();

  // Informer l'utilisateur
  \Drupal::messenger()->addStatus('Le module Client Demandes a été installé avec succès.');
}

/**
 * Implements hook_uninstall().
 */
function client_demandes_uninstall()
{
  // Nettoyer les configurations
  \Drupal::configFactory()->getEditable('client_demandes.settings')->delete();

  // Note: nous ne supprimons pas les rôles, vocabulaires ou types de contenu
  // car ils pourraient être utilisés par d'autres modules
}

/**
 * Créer les champs du profil prestataire s'ils n'existent pas.
 */
function _client_demandes_create_profile_fields()
{
  // Créer le type de profil prestataire s'il n'existe pas
  $profile_type = \Drupal::entityTypeManager()->getStorage('profile_type')->load('prestataire');
  if (!$profile_type) {
    $profile_type = \Drupal\profile\Entity\ProfileType::create([
      'id' => 'prestataire',
      'label' => 'Profil Prestataire',
      'description' => 'Profil pour les prestataires de services',
      'registration' => FALSE,
      'multiple' => FALSE,
      'roles' => ['prestataire'],
    ]);
    $profile_type->save();
  }

  // Créer le champ field_categorie pour le profil prestataire
  $field_storage = \Drupal\field\Entity\FieldStorageConfig::loadByName('profile', 'field_categorie');
  if (!$field_storage) {
    $field_storage = \Drupal\field\Entity\FieldStorageConfig::create([
      'field_name' => 'field_categorie',
      'entity_type' => 'profile',
      'type' => 'entity_reference',
      'settings' => [
        'target_type' => 'taxonomy_term',
      ],
      'cardinality' => -1,
    ]);
    $field_storage->save();
  }

  $field_config = \Drupal\field\Entity\FieldConfig::loadByName('profile', 'prestataire', 'field_categorie');
  if (!$field_config) {
    $field_config = \Drupal\field\Entity\FieldConfig::create([
      'field_storage' => $field_storage,
      'bundle' => 'prestataire',
      'label' => 'Catégories de services',
      'description' => 'Sélectionnez les catégories de services que vous proposez',
      'required' => TRUE,
      'settings' => [
        'handler' => 'default:taxonomy_term',
        'handler_settings' => [
          'target_bundles' => [
            'categories_service' => 'categories_service',
          ],
          'sort' => [
            'field' => 'name',
            'direction' => 'asc',
          ],
          'auto_create' => FALSE,
        ],
      ],
    ]);
    $field_config->save();
  }

  // Créer le champ field_localisation pour le profil prestataire
  $field_storage_loc = \Drupal\field\Entity\FieldStorageConfig::loadByName('profile', 'field_localisation');
  if (!$field_storage_loc) {
    $field_storage_loc = \Drupal\field\Entity\FieldStorageConfig::create([
      'field_name' => 'field_localisation',
      'entity_type' => 'profile',
      'type' => 'string',
      'settings' => [
        'max_length' => 255,
        'case_sensitive' => FALSE,
      ],
      'cardinality' => 1,
    ]);
    $field_storage_loc->save();
  }

  $field_config_loc = \Drupal\field\Entity\FieldConfig::loadByName('profile', 'prestataire', 'field_localisation');
  if (!$field_config_loc) {
    $field_config_loc = \Drupal\field\Entity\FieldConfig::create([
      'field_storage' => $field_storage_loc,
      'bundle' => 'prestataire',
      'label' => 'Localisation',
      'description' => 'Votre zone géographique dintervention',
      'required' => TRUE,
      'settings' => [
        'max_length' => 255,
      ],
    ]);
    $field_config_loc->save();
  }
}
