<?php

namespace Drupal\client_demandes\Controller;

use Drupal\Core\Controller\ControllerBase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Drupal\commerce_order\Entity\Order;
use Drupal\commerce_product\Entity\ProductVariation;
use Drupal\node\Entity\Node;
use Drupal\commerce_price\Price;
use Drupal\commerce_order\Entity\OrderItem;

class PaiementController extends ControllerBase
{

  public function checkoutComplet(Request $request)
  {
    return $this->createCheckout($request, 'complet');
  }

  public function checkoutPartiel(Request $request)
  {
    return $this->createCheckout($request, 'partiel');
  }

  private function createCheckout(Request $request, $mode)
  {
    $devis_id = $request->get('devis_id');
    $devis = Node::load($devis_id);

    if (!$devis || $devis->getType() !== 'devis') {
      $this->messenger()->addError("Devis introuvable.");
      return $this->redirect('<front>');
    }

    // Vérifier si une commande existe déjà pour ce devis
    $existing_order = null;
    if ($devis->hasField('field_order_id') && !$devis->get('field_order_id')->isEmpty()) {
      $order_id = $devis->get('field_order_id')->value;
      $existing_order = Order::load($order_id);
    }

    if ($existing_order && $existing_order->getState()->getId() === 'draft') {
      // Utiliser la commande existante
      $order = $existing_order;

      // S'assurer que la commande est dans le panier
      $cart_session = \Drupal::service('commerce_cart.cart_session');
      $cart_session->addCartId($order->id());

      $this->messenger()->addStatus("Redirection vers votre commande existante.");
    } else {
      // Créer une nouvelle commande seulement si nécessaire
      $variation = ProductVariation::create([
        'type' => 'default',
        'title' => 'Paiement ' . ucfirst($mode),
        'sku' => 'DEVIS-' . $devis_id . '-' . $mode,
        'price' => new Price($devis->get('field_price')->value, 'EUR'),
        'status' => 1,
      ]);
      $variation->save();

      $store = \Drupal::entityTypeManager()->getStorage('commerce_store')->loadDefault();

      $order = Order::create([
        'type' => 'default',
        'store_id' => $store->id(),
        'uid' => $this->currentUser()->id(),
        'state' => 'draft',
      ]);

      $order_item = OrderItem::create([
        'type' => 'default',
        'purchased_entity' => $variation,
        'quantity' => 1,
        'unit_price' => $variation->getPrice(),
      ]);
      $order_item->save();

      $order->addItem($order_item);
      $order->save();

      // Sauvegarder l'ID de commande dans le devis
      if ($devis->hasField('field_order_id')) {
        $devis->set('field_order_id', $order->id());
        $devis->save();
      }

      // Associer au panier
      $cart_session = \Drupal::service('commerce_cart.cart_session');
      $cart_session->addCartId($order->id());
    }

    return new RedirectResponse('/checkout/' . $order->id());
  }

  public function optionsPaiement($devis)
  {
    $devis_node = Node::load($devis);

    if (!$devis_node || $devis_node->getType() !== 'devis') {
      $this->messenger()->addError("Devis introuvable.");
      return $this->redirect('<front>');
    }

    return [
      '#theme' => 'paiement_options',
      '#devis' => $devis_node,
      '#attached' => [
        'library' => [
          'client_demandes/paiement_options',
        ],
      ],
    ];
  }
}
