<?php

namespace Drupal\client_demandes\Controller;

use <PERSON><PERSON><PERSON>\Core\Controller\ControllerBase;
use <PERSON><PERSON><PERSON>\node\Entity\Node;
use <PERSON><PERSON>al\user\Entity\User;

/**
 * Contrôleur pour tester les notifications.
 */
class NotificationTestController extends ControllerBase
{

  /**
   * Page de test pour les notifications.
   */
  public function testPage()
  {
    $content = [];

    $content['title'] = [
      '#markup' => '<h1>Test des Notifications</h1>',
    ];

    $content['description'] = [
      '#markup' => '<p>Cette page permet de tester le système de notifications pour les nouvelles demandes.</p>',
    ];

    // Formulaire de test
    $content['test_form'] = [
      '#markup' => '
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>🧪 Test de Notification</h3>
          <p>Pour tester le système :</p>
          <ol>
            <li><strong>Créer un prestataire :</strong> Créez un utilisateur avec le rôle "prestataire"</li>
            <li><strong>Ajouter des catégories :</strong> Assignez des catégories métier au prestataire</li>
            <li><strong>Publier une demande :</strong> Créez une nouvelle demande avec une catégorie correspondante</li>
            <li><strong>Vérifier :</strong> Le prestataire devrait recevoir un email et un message interne</li>
          </ol>
        </div>
      ',
    ];

    // Statistiques actuelles
    $stats = $this->getNotificationStats();

    $content['stats'] = [
      '#markup' => '
        <div style="background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>📊 Statistiques</h3>
          <ul>
            <li><strong>Prestataires actifs :</strong> ' . $stats['prestataires'] . '</li>
            <li><strong>Prestataires avec catégories :</strong> ' . $stats['prestataires_avec_categories'] . '</li>
            <li><strong>Demandes publiées :</strong> ' . $stats['demandes'] . '</li>
            <li><strong>Messages envoyés :</strong> ' . $stats['messages'] . '</li>
          </ul>
        </div>
      ',
    ];

    // Bouton de test manuel
    $content['manual_test'] = [
      '#markup' => '
        <div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>🔧 Test Manuel</h3>
          <p>Vous pouvez tester manuellement en créant une demande fictive :</p>
          <a href="/admin/content/add/demande" class="button" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
            ➕ Créer une demande de test
          </a>
        </div>
      ',
    ];

    // Logs récents
    $content['logs'] = [
      '#markup' => '
        <div style="background: #f0f0f0; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>📝 Logs Récents</h3>
          <p>Consultez les logs Drupal pour voir les notifications envoyées :</p>
          <a href="/admin/reports/dblog" class="button" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
            📋 Voir les logs
          </a>
        </div>
      ',
    ];

    return $content;
  }

  /**
   * Récupère les statistiques de notification.
   */
  private function getNotificationStats()
  {
    // Compter les prestataires
    $prestataires_query = \Drupal::entityQuery('user')
      ->condition('status', 1)
      ->condition('roles', 'prestataire')
      ->accessCheck(TRUE);
    $prestataires_count = $prestataires_query->count()->execute();

    // Compter les prestataires avec catégories (vérifier si le champ existe)
    $prestataires_avec_categories_count = 0;
    $user_field_manager = \Drupal::service('entity_field.manager');
    $user_fields = $user_field_manager->getFieldDefinitions('user', 'user');

    if (isset($user_fields['field_categorie'])) {
      $prestataires_avec_categories_query = \Drupal::entityQuery('user')
        ->condition('status', 1)
        ->condition('roles', 'prestataire')
        ->exists('field_categorie')
        ->accessCheck(TRUE);
      $prestataires_avec_categories_count = $prestataires_avec_categories_query->count()->execute();
    }

    // Compter les demandes
    $demandes_query = \Drupal::entityQuery('node')
      ->condition('type', 'demande')
      ->condition('status', 1)
      ->accessCheck(TRUE);
    $demandes_count = $demandes_query->count()->execute();

    // Compter les messages (si le module Message est installé)
    $messages_count = 0;
    if (\Drupal::moduleHandler()->moduleExists('message')) {
      $messages_query = \Drupal::entityQuery('message')
        ->condition('template', 'nouvelle_demande_prestataire')
        ->accessCheck(TRUE);
      $messages_count = $messages_query->count()->execute();
    }

    return [
      'prestataires' => $prestataires_count,
      'prestataires_avec_categories' => $prestataires_avec_categories_count,
      'demandes' => $demandes_count,
      'messages' => $messages_count,
    ];
  }

  /**
   * Test de création d'une demande fictive.
   */
  public function createTestDemande()
  {
    try {
      // Créer une demande de test
      $demande = Node::create([
        'type' => 'demande',
        'title' => 'Test - Demande de notification ' . date('Y-m-d H:i:s'),
        'field_description' => 'Ceci est une demande de test pour vérifier le système de notifications.',
        'field_price' => [
          'number' => 1000,
          'currency_code' => 'EUR',
        ],
        'field_localisation' => 'Test, France',
        'status' => 1,
        'uid' => \Drupal::currentUser()->id(),
      ]);

      // Ajouter une catégorie si elle existe
      if ($demande->hasField('field_categorie')) {
        $terms = \Drupal::entityTypeManager()
          ->getStorage('taxonomy_term')
          ->loadByProperties(['vid' => 'categories_services']);

        if (!empty($terms)) {
          $first_term = reset($terms);
          $demande->set('field_categorie', $first_term->id());
        }
      }

      $demande->save();

      \Drupal::messenger()->addStatus('Demande de test créée avec succès ! ID: ' . $demande->id());
    } catch (\Exception $e) {
      \Drupal::messenger()->addError('Erreur lors de la création de la demande de test: ' . $e->getMessage());
    }

    return $this->redirect('client_demandes.notification_test');
  }

  /**
   * Page de test pour les redirections de connexion.
   */
  public function loginRedirectTest()
  {
    $content = [];

    $content['title'] = [
      '#markup' => '<h1>Test des Redirections de Connexion</h1>',
    ];

    $content['description'] = [
      '#markup' => '<p>Cette page permet de tester les redirections automatiques après connexion.</p>',
    ];

    // Informations sur l'utilisateur actuel
    $current_user = \Drupal::currentUser();
    $user_info = '';

    if ($current_user->isAuthenticated()) {
      $account = \Drupal\user\Entity\User::load($current_user->id());
      $roles = $account->getRoles();
      $user_info = '
        <div style="background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>👤 Utilisateur Actuel</h3>
          <ul>
            <li><strong>Nom :</strong> ' . $account->getDisplayName() . '</li>
            <li><strong>Email :</strong> ' . $account->getEmail() . '</li>
            <li><strong>Rôles :</strong> ' . implode(', ', $roles) . '</li>
          </ul>
        </div>
      ';
    } else {
      $user_info = '
        <div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>⚠️ Non connecté</h3>
          <p>Vous devez être connecté pour tester les redirections.</p>
        </div>
      ';
    }

    $content['user_info'] = [
      '#markup' => $user_info,
    ];

    // Instructions de test
    $content['instructions'] = [
      '#markup' => '
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>🧪 Comment Tester</h3>
          <ol>
            <li><strong>Créer des utilisateurs test :</strong>
              <ul>
                <li>Un utilisateur avec le rôle "prestataire"</li>
                <li>Un utilisateur avec le rôle "client"</li>
              </ul>
            </li>
            <li><strong>Se déconnecter</strong> (si connecté)</li>
            <li><strong>Se reconnecter</strong> avec un compte prestataire</li>
            <li><strong>Vérifier</strong> la redirection vers <code>/prestataire/dashboard</code></li>
            <li><strong>Répéter</strong> avec un compte client</li>
            <li><strong>Vérifier</strong> la redirection vers <code>/client/dashboard</code></li>
          </ol>
        </div>
      ',
    ];

    // Liens utiles
    $content['links'] = [
      '#markup' => '
        <div style="background: #cce5ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>🔗 Liens Utiles</h3>
          <ul>
            <li><a href="/user/logout" class="button" style="background: #dc3545; color: white; padding: 8px 15px; text-decoration: none; border-radius: 5px;">🚪 Se déconnecter</a></li>
            <li><a href="/user/login" class="button" style="background: #007bff; color: white; padding: 8px 15px; text-decoration: none; border-radius: 5px;">🔑 Page de connexion</a></li>
            <li><a href="/prestataire/dashboard" class="button" style="background: #28a745; color: white; padding: 8px 15px; text-decoration: none; border-radius: 5px;">📊 Dashboard Prestataire</a></li>
            <li><a href="/client/dashboard" class="button" style="background: #17a2b8; color: white; padding: 8px 15px; text-decoration: none; border-radius: 5px;">📋 Dashboard Client</a></li>
          </ul>
        </div>
      ',
    ];

    return $content;
  }

  /**
   * Page de test pour les commandes Commerce.
   */
  public function commerceOrderTest()
  {
    $content = [];

    $content['title'] = [
      '#markup' => '<h1>Test des Commandes Commerce</h1>',
    ];

    $content['description'] = [
      '#markup' => '<p>Cette page permet de vérifier l\'affichage des dates et IDs dans les commandes Commerce.</p>',
    ];

    // Lister les commandes récentes
    $query = \Drupal::entityQuery('commerce_order')
      ->sort('order_id', 'DESC')
      ->range(0, 10)
      ->accessCheck(TRUE);

    $order_ids = $query->execute();

    if (!empty($order_ids)) {
      $orders = \Drupal\commerce_order\Entity\Order::loadMultiple($order_ids);

      $orders_info = '<div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <h3>📋 Commandes Récentes</h3>
        <table style="width: 100%; border-collapse: collapse;">
          <thead>
            <tr style="background: #e9ecef;">
              <th style="padding: 10px; border: 1px solid #dee2e6;">ID</th>
              <th style="padding: 10px; border: 1px solid #dee2e6;">Numéro</th>
              <th style="padding: 10px; border: 1px solid #dee2e6;">Date</th>
              <th style="padding: 10px; border: 1px solid #dee2e6;">Statut</th>
              <th style="padding: 10px; border: 1px solid #dee2e6;">Client</th>
              <th style="padding: 10px; border: 1px solid #dee2e6;">Actions</th>
            </tr>
          </thead>
          <tbody>';

      foreach ($orders as $order) {
        $placed_time = $order->getPlacedTime() ? date('d/m/Y H:i', $order->getPlacedTime()) : 'Non définie';
        $order_number = $order->getOrderNumber() ?: 'CMD-' . $order->id();
        $customer = $order->getCustomer();
        $customer_name = $customer ? $customer->getDisplayName() : 'Anonyme';

        $orders_info .= '
          <tr>
            <td style="padding: 8px; border: 1px solid #dee2e6;">' . $order->id() . '</td>
            <td style="padding: 8px; border: 1px solid #dee2e6;">' . $order_number . '</td>
            <td style="padding: 8px; border: 1px solid #dee2e6;">' . $placed_time . '</td>
            <td style="padding: 8px; border: 1px solid #dee2e6;">' . $order->getState()->getLabel() . '</td>
            <td style="padding: 8px; border: 1px solid #dee2e6;">' . $customer_name . '</td>
            <td style="padding: 8px; border: 1px solid #dee2e6;">
              <a href="/admin/commerce/orders/' . $order->id() . '" class="button" style="background: #007bff; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px; font-size: 0.8em;">Voir</a>
            </td>
          </tr>';
      }

      $orders_info .= '</tbody></table></div>';

      $content['orders_list'] = [
        '#markup' => $orders_info,
      ];
    } else {
      $content['no_orders'] = [
        '#markup' => '
          <div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3>⚠️ Aucune Commande</h3>
            <p>Aucune commande trouvée dans le système.</p>
          </div>
        ',
      ];
    }

    // Instructions pour créer une commande de test
    $content['instructions'] = [
      '#markup' => '
        <div style="background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>🧪 Créer une Commande de Test</h3>
          <p>Pour tester l\'affichage des commandes :</p>
          <ol>
            <li>Créez une demande</li>
            <li>Créez un devis pour cette demande</li>
            <li>Acceptez le devis (cela créera automatiquement une commande)</li>
            <li>Vérifiez que la commande affiche bien la date et l\'ID</li>
          </ol>
          <a href="/admin/commerce/orders" class="button" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
            📋 Voir toutes les commandes
          </a>
        </div>
      ',
    ];

    return $content;
  }

  /**
   * Page de diagnostic des champs.
   */
  public function fieldDiagnostic()
  {
    $content = [];

    $content['title'] = [
      '#markup' => '<h1>Diagnostic des Champs</h1>',
    ];

    $content['description'] = [
      '#markup' => '<p>Cette page vérifie la configuration des champs nécessaires au bon fonctionnement du module.</p>',
    ];

    // Alerte importante si le champ n'existe pas
    $content['alert'] = [
      '#markup' => '
        <div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 8px; margin: 20px 0;">
          <h3>⚠️ Action Requise</h3>
          <p><strong>Le champ <code>field_categorie</code> doit être créé sur l\'entité utilisateur pour que le système de notifications fonctionne correctement.</strong></p>
          <p>Sans ce champ, tous les prestataires recevront toutes les notifications, sans filtre par catégorie.</p>
        </div>
      ',
    ];

    // Vérifier les champs sur l'entité utilisateur
    $user_field_manager = \Drupal::service('entity_field.manager');
    $user_fields = $user_field_manager->getFieldDefinitions('user', 'user');

    $user_custom_fields = [];
    foreach ($user_fields as $field_name => $field_definition) {
      if (strpos($field_name, 'field_') === 0) {
        $user_custom_fields[$field_name] = [
          'label' => $field_definition->getLabel(),
          'type' => $field_definition->getType(),
          'required' => $field_definition->isRequired(),
        ];
      }
    }

    // Vérifier les champs sur l'entité demande
    $node_fields = $user_field_manager->getFieldDefinitions('node', 'demande');
    $demande_custom_fields = [];
    foreach ($node_fields as $field_name => $field_definition) {
      if (strpos($field_name, 'field_') === 0) {
        $demande_custom_fields[$field_name] = [
          'label' => $field_definition->getLabel(),
          'type' => $field_definition->getType(),
          'required' => $field_definition->isRequired(),
        ];
      }
    }

    // Affichage des champs utilisateur
    $user_fields_html = '<div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
      <h3>👤 Champs Utilisateur</h3>
      <table style="width: 100%; border-collapse: collapse;">
        <thead>
          <tr style="background: #e9ecef;">
            <th style="padding: 10px; border: 1px solid #dee2e6;">Nom du champ</th>
            <th style="padding: 10px; border: 1px solid #dee2e6;">Label</th>
            <th style="padding: 10px; border: 1px solid #dee2e6;">Type</th>
            <th style="padding: 10px; border: 1px solid #dee2e6;">Requis</th>
          </tr>
        </thead>
        <tbody>';

    foreach ($user_custom_fields as $field_name => $field_info) {
      $required_badge = $field_info['required'] ? '<span style="background: #dc3545; color: white; padding: 2px 6px; border-radius: 3px; font-size: 0.8em;">Requis</span>' : '';
      $field_class = $field_name === 'field_categorie' ? 'style="background: #d4edda;"' : '';

      $user_fields_html .= '
        <tr ' . $field_class . '>
          <td style="padding: 8px; border: 1px solid #dee2e6;"><code>' . $field_name . '</code></td>
          <td style="padding: 8px; border: 1px solid #dee2e6;">' . $field_info['label'] . '</td>
          <td style="padding: 8px; border: 1px solid #dee2e6;">' . $field_info['type'] . '</td>
          <td style="padding: 8px; border: 1px solid #dee2e6;">' . $required_badge . '</td>
        </tr>';
    }

    $user_fields_html .= '</tbody></table></div>';

    // Vérification spécifique du champ field_categorie
    $categorie_status = '';
    if (isset($user_custom_fields['field_categorie'])) {
      $categorie_status = '
        <div style="background: #d4edda; padding: 15px; border-radius: 8px; margin: 20px 0;">
          <h4>✅ Champ field_categorie trouvé</h4>
          <ul>
            <li><strong>Type :</strong> ' . $user_custom_fields['field_categorie']['type'] . '</li>
            <li><strong>Label :</strong> ' . $user_custom_fields['field_categorie']['label'] . '</li>
            <li><strong>Requis :</strong> ' . ($user_custom_fields['field_categorie']['required'] ? 'Oui' : 'Non') . '</li>
          </ul>
        </div>';
    } else {
      $categorie_status = '
        <div style="background: #f8d7da; padding: 15px; border-radius: 8px; margin: 20px 0;">
          <h4>❌ Champ field_categorie manquant</h4>
          <p>Le champ <code>field_categorie</code> n\'existe pas sur l\'entité utilisateur.</p>
          <p><strong>Solution :</strong> Créez ce champ via l\'interface d\'administration Drupal :</p>
          <ol>
            <li>Allez dans <a href="/admin/config/people/accounts/fields">Configuration > Comptes utilisateur > Gérer les champs</a></li>
            <li>Ajoutez un nouveau champ de type "Entity reference"</li>
            <li>Nommez-le "field_categorie"</li>
            <li>Configurez-le pour référencer la taxonomie "categories_service"</li>
          </ol>
          <p><strong>Ou utilisez la création automatique :</strong></p>
          <a href="/admin/client-demandes/create-user-categorie-field" class="button" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
            🔧 Créer automatiquement le champ
          </a>
        </div>';
    }

    // Vérification des champs Stripe
    $stripe_status = '';
    $stripe_account_exists = isset($user_custom_fields['field_stripe_account_id']);
    $stripe_token_exists = isset($user_custom_fields['field_stripe_access_token']);

    if ($stripe_account_exists && $stripe_token_exists) {
      $stripe_status = '
        <div style="background: #d4edda; padding: 15px; border-radius: 8px; margin: 20px 0;">
          <h4>✅ Champs Stripe trouvés</h4>
          <ul>
            <li><strong>field_stripe_account_id :</strong> ' . $user_custom_fields['field_stripe_account_id']['label'] . '</li>
            <li><strong>field_stripe_access_token :</strong> ' . $user_custom_fields['field_stripe_access_token']['label'] . '</li>
          </ul>
          <p><em>Ces champs sont réservés aux prestataires pour Stripe Connect.</em></p>
        </div>';
    } else {
      $missing_fields = [];
      if (!$stripe_account_exists) $missing_fields[] = 'field_stripe_account_id';
      if (!$stripe_token_exists) $missing_fields[] = 'field_stripe_access_token';

      $stripe_status = '
        <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;">
          <h4>⚠️ Champs Stripe manquants</h4>
          <p>Les champs suivants sont manquants : <code>' . implode('</code>, <code>', $missing_fields) . '</code></p>
          <p>Ces champs sont nécessaires pour que les prestataires puissent connecter leur compte Stripe.</p>
          <p><strong>Création automatique :</strong></p>
          <a href="/admin/client-demandes/create-stripe-fields" class="button" style="background: #635bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
            💳 Créer les champs Stripe
          </a>
        </div>';
    }

    $content['user_fields'] = [
      '#markup' => $user_fields_html,
    ];

    $content['categorie_status'] = [
      '#markup' => $categorie_status,
    ];

    $content['stripe_status'] = [
      '#markup' => $stripe_status,
    ];

    // Vérification des champs du profil prestataire
    $profile_status = '';
    try {
      $profile_field_manager = \Drupal::service('entity_field.manager');
      $profile_fields = $profile_field_manager->getFieldDefinitions('profile', 'prestataire');

      $required_fields = ['field_categorie', 'field_telephone'];
      $missing_fields = [];

      foreach ($required_fields as $field_name) {
        if (!isset($profile_fields[$field_name])) {
          $missing_fields[] = $field_name;
        }
      }

      if (empty($missing_fields)) {
        $profile_status = '
          <div style="background: #d4edda; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h4>✅ Champs du Profil Prestataire OK</h4>
            <p>Tous les champs requis sont présents sur le profil prestataire.</p>
          </div>';
      } else {
        $profile_status = '
          <div style="background: #f8d7da; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h4>❌ Champs du Profil Prestataire Manquants</h4>
            <p>Les champs suivants sont manquants : <code>' . implode('</code>, <code>', $missing_fields) . '</code></p>
            <p><strong>Création automatique :</strong></p>
            <a href="/admin/client-demandes/create-profile-fields" class="button" style="background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
              🔧 Créer les champs du profil prestataire
            </a>
          </div>';
      }
    } catch (\Exception $e) {
      $profile_status = '
        <div style="background: #f8d7da; padding: 15px; border-radius: 8px; margin: 20px 0;">
          <h4>❌ Erreur lors de la vérification du profil</h4>
          <p>Erreur : ' . $e->getMessage() . '</p>
        </div>';
    }

    $content['profile_status'] = [
      '#markup' => $profile_status,
    ];

    return $content;
  }

  /**
   * Crée automatiquement le champ field_categorie sur l'entité utilisateur.
   */
  public function createUserCategorieField()
  {
    $success = _client_demandes_create_user_categorie_field();

    if ($success) {
      \Drupal::messenger()->addStatus('Le champ field_categorie a été créé avec succès sur l\'entité utilisateur.');
    } else {
      \Drupal::messenger()->addError('Erreur lors de la création du champ field_categorie. Consultez les logs pour plus de détails.');
    }

    // Rediriger vers la page de diagnostic
    return $this->redirect('client_demandes.field_diagnostic');
  }

  /**
   * Page de test du filtrage par catégorie.
   */
  public function categoryFilterTest()
  {
    $content = [];

    $content['title'] = [
      '#markup' => '<h1>Test du Filtrage par Catégorie</h1>',
    ];

    $content['description'] = [
      '#markup' => '<p>Cette page teste le système de filtrage des notifications par catégorie.</p>',
    ];

    // Vérifier si le champ existe
    $field_exists = _client_demandes_check_user_categorie_field();

    if (!$field_exists) {
      $content['error'] = [
        '#markup' => '
          <div style="background: #f8d7da; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3>❌ Champ Manquant</h3>
            <p>Le champ <code>field_categorie</code> n\'existe pas sur l\'entité utilisateur.</p>
            <a href="/admin/client-demandes/field-diagnostic" class="button">Diagnostiquer</a>
          </div>
        ',
      ];
      return $content;
    }

    // Statistiques des prestataires par catégorie
    $categories_stats = $this->getCategoriesStats();

    $stats_html = '<div style="background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0;">
      <h3>📊 Statistiques par Catégorie</h3>
      <table style="width: 100%; border-collapse: collapse;">
        <thead>
          <tr style="background: #d4edda;">
            <th style="padding: 10px; border: 1px solid #c3e6cb;">Catégorie</th>
            <th style="padding: 10px; border: 1px solid #c3e6cb;">Prestataires</th>
          </tr>
        </thead>
        <tbody>';

    foreach ($categories_stats as $category_name => $count) {
      $stats_html .= '
        <tr>
          <td style="padding: 8px; border: 1px solid #c3e6cb;">' . $category_name . '</td>
          <td style="padding: 8px; border: 1px solid #c3e6cb; text-align: center;">' . $count . '</td>
        </tr>';
    }

    $stats_html .= '</tbody></table></div>';

    $content['stats'] = [
      '#markup' => $stats_html,
    ];

    // Test de simulation
    $content['test'] = [
      '#markup' => '
        <div style="background: #cce5ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>🧪 Test de Simulation</h3>
          <p>Pour tester le filtrage :</p>
          <ol>
            <li>Créez des utilisateurs prestataires</li>
            <li>Assignez-leur des catégories via <code>field_categorie</code></li>
            <li>Créez une demande avec une catégorie spécifique</li>
            <li>Vérifiez les logs pour voir quels prestataires sont notifiés</li>
          </ol>
          <a href="/admin/reports/dblog" class="button" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
            📋 Voir les logs
          </a>
        </div>
      ',
    ];

    return $content;
  }

  /**
   * Récupère les statistiques des prestataires par catégorie.
   */
  private function getCategoriesStats()
  {
    $stats = [];

    // Récupérer toutes les catégories
    $terms = \Drupal::entityTypeManager()
      ->getStorage('taxonomy_term')
      ->loadByProperties(['vid' => 'categories_service']);

    foreach ($terms as $term) {
      // Compter les prestataires pour cette catégorie
      $query = \Drupal::entityQuery('user')
        ->condition('status', 1)
        ->condition('roles', 'prestataire')
        ->condition('field_categorie', $term->id())
        ->accessCheck(TRUE);

      $count = $query->count()->execute();
      $stats[$term->label()] = $count;
    }

    // Ajouter les prestataires sans catégorie
    $query_no_cat = \Drupal::entityQuery('user')
      ->condition('status', 1)
      ->condition('roles', 'prestataire')
      ->notExists('field_categorie')
      ->accessCheck(TRUE);

    $no_cat_count = $query_no_cat->count()->execute();
    if ($no_cat_count > 0) {
      $stats['Sans catégorie'] = $no_cat_count;
    }

    return $stats;
  }

  /**
   * Page de test de l'intégration Stripe Connect.
   */
  public function stripeConnectTest()
  {
    $content = [];

    $content['title'] = [
      '#markup' => '<h1>Test de l\'Intégration Stripe Connect</h1>',
    ];

    $content['description'] = [
      '#markup' => '<p>Cette page teste l\'intégration complète de Stripe Connect.</p>',
    ];

    // Vérifier la configuration Stripe
    $config = \Drupal::config('client_demandes.settings');
    $stripe_client_id = $config->get('stripe_client_id');
    $stripe_secret_key = $config->get('stripe_secret_key');
    $stripe_mode = $config->get('stripe_mode') ?: 'test';

    $config_status = '';
    if (empty($stripe_client_id) || empty($stripe_secret_key)) {
      $config_status = '
        <div style="background: #f8d7da; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>❌ Configuration Incomplète</h3>
          <p>Les clés Stripe ne sont pas configurées.</p>
          <a href="/admin/config/services/client-demandes/stripe" class="button">Configurer Stripe</a>
        </div>
      ';
    } else {
      $config_status = '
        <div style="background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>✅ Configuration OK</h3>
          <ul>
            <li><strong>Mode :</strong> ' . ucfirst($stripe_mode) . '</li>
            <li><strong>Client ID :</strong> ' . substr($stripe_client_id, 0, 10) . '...</li>
            <li><strong>Clé secrète :</strong> Configurée</li>
          </ul>
        </div>
      ';
    }

    $content['config_status'] = [
      '#markup' => $config_status,
    ];

    // URLs de test
    $base_url = \Drupal::request()->getSchemeAndHttpHost();
    $content['test_urls'] = [
      '#markup' => '
        <div style="background: #cce5ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>🔗 URLs de Test</h3>
          <ul>
            <li><a href="/prestataire/dashboard" target="_blank">Tableau de bord prestataire</a></li>
            <li><a href="/prestataire/stripe/configure" target="_blank">Configuration Stripe Connect</a></li>
            <li><a href="' . $base_url . '/prestataire/stripe/callback?code=test&state=1" target="_blank">Test callback (simulation)</a></li>
          </ul>
        </div>
      ',
    ];

    // Instructions de test
    $content['test_instructions'] = [
      '#markup' => '
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>🧪 Instructions de Test</h3>
          <ol>
            <li>Configurez vos clés Stripe dans <a href="/admin/config/services/client-demandes/stripe">la page de configuration</a></li>
            <li>Connectez-vous en tant que prestataire</li>
            <li>Accédez au <a href="/prestataire/dashboard">tableau de bord prestataire</a></li>
            <li>Cliquez sur "Connecter Stripe" dans la section Stripe Connect</li>
            <li>Vous devriez être redirigé vers le formulaire Stripe OAuth</li>
            <li>Après autorisation, vous serez redirigé vers le callback</li>
            <li>Votre compte Stripe sera connecté et affiché dans le tableau de bord</li>
          </ol>
        </div>
      ',
    ];

    // Informations de debug
    $current_user = \Drupal::currentUser();
    $debug_info = '
      <div style="background: #e9ecef; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <h3>🔍 Informations de Debug</h3>
        <ul>
          <li><strong>Utilisateur actuel :</strong> ' . $current_user->getDisplayName() . ' (ID: ' . $current_user->id() . ')</li>
          <li><strong>Rôles :</strong> ' . implode(', ', $current_user->getRoles()) . '</li>
          <li><strong>URL de base :</strong> ' . $base_url . '</li>
          <li><strong>Redirect URI :</strong> ' . $base_url . '/prestataire/stripe/callback</li>
        </ul>
      </div>
    ';

    $content['debug_info'] = [
      '#markup' => $debug_info,
    ];

    return $content;
  }

  /**
   * Crée automatiquement les champs Stripe sur l'entité utilisateur.
   */
  public function createStripeFields()
  {
    $success = _client_demandes_create_stripe_fields();

    if ($success) {
      \Drupal::messenger()->addStatus('Les champs Stripe ont été créés avec succès sur l\'entité utilisateur.');
    } else {
      \Drupal::messenger()->addError('Erreur lors de la création des champs Stripe. Consultez les logs pour plus de détails.');
    }

    // Rediriger vers la page de diagnostic
    return $this->redirect('client_demandes.field_diagnostic');
  }

  /**
   * Page de test pour le checkout Commerce.
   */
  public function checkoutTest()
  {
    $content = [];

    $content['title'] = [
      '#markup' => '<h1>Test du Checkout Commerce</h1>',
    ];

    $content['description'] = [
      '#markup' => '<p>Cette page permet de tester le processus de checkout.</p>',
    ];

    // Lister les commandes existantes
    $query = \Drupal::entityQuery('commerce_order')
      ->condition('state', 'draft')
      ->sort('order_id', 'DESC')
      ->range(0, 10)
      ->accessCheck(TRUE);

    $order_ids = $query->execute();

    if (!empty($order_ids)) {
      $orders = \Drupal\commerce_order\Entity\Order::loadMultiple($order_ids);

      $orders_html = '<div style="background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <h3>📋 Commandes en Attente de Paiement</h3>
        <table style="width: 100%; border-collapse: collapse;">
          <thead>
            <tr style="background: #d4edda;">
              <th style="padding: 10px; border: 1px solid #c3e6cb;">ID</th>
              <th style="padding: 10px; border: 1px solid #c3e6cb;">Numéro</th>
              <th style="padding: 10px; border: 1px solid #c3e6cb;">Client</th>
              <th style="padding: 10px; border: 1px solid #c3e6cb;">Total</th>
              <th style="padding: 10px; border: 1px solid #c3e6cb;">Actions</th>
            </tr>
          </thead>
          <tbody>';

      foreach ($orders as $order) {
        $customer = $order->getCustomer();
        $customer_name = $customer ? $customer->getDisplayName() : 'Anonyme';
        $total = $order->getTotalPrice() ? $order->getTotalPrice()->getNumber() . ' €' : 'N/A';
        $order_number = $order->getOrderNumber() ?: 'CMD-' . $order->id();

        $orders_html .= '
          <tr>
            <td style="padding: 8px; border: 1px solid #c3e6cb;">' . $order->id() . '</td>
            <td style="padding: 8px; border: 1px solid #c3e6cb;">' . $order_number . '</td>
            <td style="padding: 8px; border: 1px solid #c3e6cb;">' . $customer_name . '</td>
            <td style="padding: 8px; border: 1px solid #c3e6cb;">' . $total . '</td>
            <td style="padding: 8px; border: 1px solid #c3e6cb;">
              <a href="/checkout/' . $order->id() . '" class="button" style="background: #007bff; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px; font-size: 0.8em;">💳 Payer</a>
            </td>
          </tr>';
      }

      $orders_html .= '</tbody></table></div>';

      $content['orders_list'] = [
        '#markup' => $orders_html,
      ];
    } else {
      $content['no_orders'] = [
        '#markup' => '
          <div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3>⚠️ Aucune Commande en Attente</h3>
            <p>Il n\'y a actuellement aucune commande en attente de paiement.</p>
          </div>
        ',
      ];
    }

    // Bouton pour créer une commande de test
    $content['create_test'] = [
      '#markup' => '
        <div style="background: #cce5ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>🧪 Créer une Commande de Test</h3>
          <p>Créez une commande de test pour tester le processus de checkout.</p>
          <a href="/admin/client-demandes/create-test-order" class="button" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
            ➕ Créer une commande de test
          </a>
        </div>
      ',
    ];

    return $content;
  }

  /**
   * Page pour accepter des devis réels et créer des commandes.
   */
  public function addToCartTest()
  {
    $content = [];

    $content['title'] = [
      '#markup' => '<h1>Accepter des Devis Réels</h1>',
    ];

    $content['description'] = [
      '#markup' => '<p>Acceptez des devis réels pour créer des commandes et pouvoir aller au checkout.</p>',
    ];

    // Lister les devis en attente (status = pending)
    $query = \Drupal::entityQuery('node')
      ->condition('type', 'devis')
      ->condition('status', 1)
      ->condition('field_status', 'pending')
      ->sort('created', 'DESC')
      ->range(0, 20)
      ->accessCheck(TRUE);

    $devis_ids = $query->execute();

    if (!empty($devis_ids)) {
      $devis_nodes = \Drupal\node\Entity\Node::loadMultiple($devis_ids);

      $devis_html = '<div style="background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <h3>📋 Devis en Attente d\'Acceptation</h3>
        <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
          <thead>
            <tr style="background: #d4edda;">
              <th style="padding: 12px; border: 1px solid #c3e6cb; text-align: left;">Devis</th>
              <th style="padding: 12px; border: 1px solid #c3e6cb; text-align: left;">Demande</th>
              <th style="padding: 12px; border: 1px solid #c3e6cb; text-align: left;">Prestataire</th>
              <th style="padding: 12px; border: 1px solid #c3e6cb; text-align: left;">Prix</th>
              <th style="padding: 12px; border: 1px solid #c3e6cb; text-align: left;">Action</th>
            </tr>
          </thead>
          <tbody>';

      foreach ($devis_nodes as $devis) {
        $demande = null;
        $demande_title = 'Demande inconnue';
        if ($devis->hasField('field_demande_ref') && !$devis->get('field_demande_ref')->isEmpty()) {
          $demande = $devis->get('field_demande_ref')->entity;
          $demande_title = $demande ? $demande->getTitle() : 'Demande supprimée';
        }

        $prestataire = $devis->getOwner();
        $prestataire_name = $prestataire ? $prestataire->getDisplayName() : 'Prestataire inconnu';

        $prix = 'Prix non défini';
        if ($devis->hasField('field_price') && !$devis->get('field_price')->isEmpty()) {
          $price_value = $devis->get('field_price')->value;
          $prix = number_format($price_value, 2, ',', ' ') . ' €';
        }

        $devis_html .= '
          <tr style="border-bottom: 1px solid #e9ecef;">
            <td style="padding: 12px; border: 1px solid #c3e6cb;">
              <strong>' . $devis->getTitle() . '</strong><br>
              <small style="color: #6c757d;">ID: ' . $devis->id() . '</small>
            </td>
            <td style="padding: 12px; border: 1px solid #c3e6cb;">' . $demande_title . '</td>
            <td style="padding: 12px; border: 1px solid #c3e6cb;">' . $prestataire_name . '</td>
            <td style="padding: 12px; border: 1px solid #c3e6cb;"><strong>' . $prix . '</strong></td>
            <td style="padding: 12px; border: 1px solid #c3e6cb;">
              <a href="/admin/client-demandes/accept-devis/' . $devis->id() . '"
                 class="button"
                 style="background: #28a745; color: white; padding: 8px 15px; text-decoration: none; border-radius: 5px; font-size: 0.9em;"
                 onclick="return confirm(\'Accepter ce devis et créer une commande ?\')">
                ✅ Accepter
              </a>
            </td>
          </tr>';
      }

      $devis_html .= '</tbody></table></div>';

      $content['devis_list'] = [
        '#markup' => $devis_html,
      ];
    } else {
      $content['no_devis'] = [
        '#markup' => '
          <div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3>⚠️ Aucun Devis en Attente</h3>
            <p>Il n\'y a actuellement aucun devis en attente d\'acceptation.</p>
            <p><strong>Pour créer des commandes :</strong></p>
            <ol>
              <li>Créez une demande</li>
              <li>Un prestataire envoie un devis</li>
              <li>Acceptez le devis ici</li>
              <li>Une commande sera créée automatiquement</li>
            </ol>
          </div>
        ',
      ];
    }

    // Test rapide pour ajouter au panier
    $content['quick_test'] = [
      '#markup' => '
        <div style="background: #d1ecf1; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #bee5eb;">
          <h3>🚀 Test Rapide du Checkout</h3>
          <p>Pour tester rapidement le checkout avec un panier rempli :</p>
          <a href="/admin/client-demandes/add-to-current-cart" class="button" style="background: #28a745; color: white; padding: 12px 20px; text-decoration: none; border-radius: 5px; font-weight: 600;">
            🛒 Ajouter un article au panier et tester
          </a>
          <p style="margin-top: 10px; font-size: 0.9em; color: #6c757d;">
            Cela ajoutera un service de test à votre panier personnel et vous redirigera vers la page du panier.
          </p>
        </div>
      ',
    ];

    // Liens utiles pour gérer les demandes et devis
    $content['useful_links'] = [
      '#markup' => '
        <div style="background: #cce5ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>🔗 Liens Utiles</h3>
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;">
            <a href="/admin/content?type=demande" class="button" style="background: #17a2b8; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; text-align: center;">
              📝 Gérer les Demandes
            </a>
            <a href="/admin/content?type=devis" class="button" style="background: #6f42c1; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; text-align: center;">
              📋 Gérer les Devis
            </a>
            <a href="/admin/commerce/orders" class="button" style="background: #fd7e14; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; text-align: center;">
              🛒 Voir les Commandes
            </a>
            <a href="/cart" class="button" style="background: #6f42c1; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; text-align: center;">
              🛒 Voir mon Panier
            </a>
            <a href="/checkout" class="button" style="background: #dc3545; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; text-align: center;">
              💳 Aller au Checkout
            </a>
          </div>
        </div>
      ',
    ];

    // Statut du panier actuel
    $cart_provider = \Drupal::service('commerce_cart.cart_provider');
    $store = \Drupal::service('commerce_store.current_store')->getStore();
    $cart = $cart_provider->getCart('default', $store);

    $cart_status = '';
    if ($cart && !$cart->getItems()->isEmpty()) {
      $items_count = count($cart->getItems());
      $total = $cart->getTotalPrice() ? $cart->getTotalPrice()->getNumber() . ' ' . $cart->getTotalPrice()->getCurrencyCode() : '0';

      $cart_status = '
        <div style="background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>🛒 Panier Actuel</h3>
          <p><strong>Articles :</strong> ' . $items_count . '</p>
          <p><strong>Total :</strong> ' . $total . '</p>
          <div style="margin-top: 15px;">
            <a href="/cart" class="button" style="background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;">
              👀 Voir le panier
            </a>
            <a href="/checkout" class="button" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
              💳 Aller au checkout
            </a>
          </div>
        </div>
      ';
    } else {
      $cart_status = '
        <div style="background: #f8d7da; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>🛒 Panier Vide</h3>
          <p>Votre panier est actuellement vide. Ajoutez des produits ci-dessus.</p>
        </div>
      ';
    }

    $content['cart_status'] = [
      '#markup' => $cart_status,
    ];

    return $content;
  }

  /**
   * Crée les champs manquants sur le profil prestataire.
   */
  public function createProfileFields()
  {
    try {
      $fields_created = 0;

      // Vérifier et créer field_categorie sur le profil prestataire
      $field_storage = \Drupal\field\Entity\FieldStorageConfig::loadByName('profile', 'field_categorie');
      if (!$field_storage) {
        $field_storage = \Drupal\field\Entity\FieldStorageConfig::create([
          'field_name' => 'field_categorie',
          'entity_type' => 'profile',
          'type' => 'entity_reference',
          'settings' => [
            'target_type' => 'taxonomy_term',
          ],
          'cardinality' => -1, // Multiple values
        ]);
        $field_storage->save();
        $fields_created++;
      }

      // Créer le field config pour le bundle prestataire
      $field_config = \Drupal\field\Entity\FieldConfig::loadByName('profile', 'prestataire', 'field_categorie');
      if (!$field_config) {
        $field_config = \Drupal\field\Entity\FieldConfig::create([
          'field_storage' => $field_storage,
          'bundle' => 'prestataire',
          'label' => 'Catégorie',
          'description' => 'Catégories de services du prestataire',
          'required' => FALSE,
          'settings' => [
            'handler' => 'default:taxonomy_term',
            'handler_settings' => [
              'target_bundles' => [
                'categories_service' => 'categories_service',
              ],
              'sort' => [
                'field' => 'name',
                'direction' => 'asc',
              ],
              'auto_create' => FALSE,
            ],
          ],
        ]);
        $field_config->save();
        $fields_created++;
      }

      // Créer field_telephone
      $field_storage_tel = \Drupal\field\Entity\FieldStorageConfig::loadByName('profile', 'field_telephone');
      if (!$field_storage_tel) {
        $field_storage_tel = \Drupal\field\Entity\FieldStorageConfig::create([
          'field_name' => 'field_telephone',
          'entity_type' => 'profile',
          'type' => 'string',
          'settings' => [
            'max_length' => 20,
          ],
          'cardinality' => 1,
        ]);
        $field_storage_tel->save();
        $fields_created++;
      }

      $field_config_tel = \Drupal\field\Entity\FieldConfig::loadByName('profile', 'prestataire', 'field_telephone');
      if (!$field_config_tel) {
        $field_config_tel = \Drupal\field\Entity\FieldConfig::create([
          'field_storage' => $field_storage_tel,
          'bundle' => 'prestataire',
          'label' => 'Téléphone',
          'description' => 'Numéro de téléphone du prestataire',
          'required' => FALSE,
          'settings' => [],
        ]);
        $field_config_tel->save();
        $fields_created++;
      }

      // Créer les termes de taxonomie par défaut
      $this->createDefaultTaxonomyTerms();

      \Drupal::messenger()->addStatus($fields_created . ' champs créés sur le profil prestataire et termes de taxonomie initialisés.');
    } catch (\Exception $e) {
      \Drupal::messenger()->addError('Erreur lors de la création des champs: ' . $e->getMessage());
      \Drupal::logger('client_demandes')->error('Erreur création champs profil: @error', ['@error' => $e->getMessage()]);
    }

    return $this->redirect('client_demandes.field_diagnostic');
  }

  /**
   * Crée une commande de test et l'ajoute au panier de l'utilisateur actuel.
   */
  public function addToCurrentUserCart()
  {
    try {
      $current_user = \Drupal::currentUser();
      $user = User::load($current_user->id());

      // Créer une variation de produit de test
      $product_variation = \Drupal\commerce_product\Entity\ProductVariation::create([
        'type' => 'default',
        'sku' => 'SERVICE-' . time(),
        'title' => 'Service de test',
        'price' => new \Drupal\commerce_price\Price('99.99', 'EUR'),
        'status' => 1,
      ]);
      $product_variation->save();

      // Récupérer le store par défaut
      $stores = \Drupal::entityTypeManager()
        ->getStorage('commerce_store')
        ->loadMultiple();
      $store = reset($stores);

      // Utiliser le service de panier pour ajouter au panier de l'utilisateur actuel
      $cart_manager = \Drupal::service('commerce_cart.cart_manager');
      $cart_provider = \Drupal::service('commerce_cart.cart_provider');

      // Obtenir ou créer le panier pour l'utilisateur actuel
      $cart = $cart_provider->getCart('default', $store, $user);
      if (!$cart) {
        $cart = $cart_provider->createCart('default', $store, $user);
      }

      // Créer un item de commande
      $order_item = \Drupal\commerce_order\Entity\OrderItem::create([
        'type' => 'default',
        'purchased_entity' => $product_variation,
        'quantity' => 1,
        'unit_price' => $product_variation->getPrice(),
        'title' => 'Service de test pour checkout',
      ]);

      // Ajouter l'item au panier
      $cart_manager->addOrderItem($cart, $order_item);

      \Drupal::messenger()->addStatus('Article ajouté au panier ! Vous pouvez maintenant aller au checkout.');

      // Rediriger vers la page du panier ou directement au checkout
      return $this->redirect('commerce_cart.page');
    } catch (\Exception $e) {
      \Drupal::messenger()->addError('Erreur lors de l\'ajout au panier: ' . $e->getMessage());
      return $this->redirect('client_demandes.add_to_cart_test');
    }
  }

  /**
   * Accepte un devis et crée une commande.
   */
  public function acceptDevis($devis_id)
  {
    try {
      // Charger le devis
      $devis = \Drupal\node\Entity\Node::load($devis_id);

      if (!$devis || $devis->getType() !== 'devis') {
        \Drupal::messenger()->addError('Devis introuvable.');
        return $this->redirect('client_demandes.add_to_cart_test');
      }

      // Vérifier que le devis est en attente
      if ($devis->hasField('field_status') && $devis->get('field_status')->value !== 'pending') {
        \Drupal::messenger()->addWarning('Ce devis a déjà été traité.');
        return $this->redirect('client_demandes.add_to_cart_test');
      }

      // Accepter le devis
      if ($devis->hasField('field_status')) {
        $devis->set('field_status', 'accepted');
        $devis->save();
      }

      // Créer la commande automatiquement (utilise la fonction existante)
      $demande = null;
      if ($devis->hasField('field_demande_ref') && !$devis->get('field_demande_ref')->isEmpty()) {
        $demande = $devis->get('field_demande_ref')->entity;
      }

      if ($demande) {
        // Utiliser la fonction existante pour créer la commande
        $order = _client_demandes_create_commerce_order($devis);

        if ($order) {
          \Drupal::messenger()->addStatus('Devis accepté ! Commande créée avec l\'ID: ' . $order->id());

          // Associer la commande au panier de l'utilisateur actuel pour le checkout
          $current_user = \Drupal::currentUser();
          if ($current_user->id() != $order->getCustomerId()) {
            // Si l'utilisateur actuel n'est pas le client de la commande,
            // on doit temporairement changer le propriétaire ou utiliser une autre approche
            \Drupal::messenger()->addWarning('Vous allez être redirigé vers le checkout de cette commande.');
          }

          // Rediriger directement vers le checkout de cette commande
          return $this->redirect('commerce_checkout.form', ['commerce_order' => $order->id()]);
        } else {
          \Drupal::messenger()->addError('Erreur lors de la création de la commande.');
        }
      } else {
        \Drupal::messenger()->addError('Demande associée introuvable.');
      }
    } catch (\Exception $e) {
      \Drupal::messenger()->addError('Erreur lors de l\'acceptation du devis: ' . $e->getMessage());
      \Drupal::logger('client_demandes')->error('Erreur acceptation devis @id: @error', [
        '@id' => $devis_id,
        '@error' => $e->getMessage(),
      ]);
    }

    return $this->redirect('client_demandes.add_to_cart_test');
  }

  /**
   * Crée des produits de test.
   */
  public function createTestProducts()
  {
    try {
      $products_created = 0;

      // Produits de test à créer
      $test_products = [
        [
          'title' => 'Service de Consultation',
          'sku' => 'CONSULT-001',
          'price' => '150.00',
          'description' => 'Consultation professionnelle d\'une heure',
        ],
        [
          'title' => 'Développement Web',
          'sku' => 'DEV-WEB-001',
          'price' => '500.00',
          'description' => 'Développement d\'un site web sur mesure',
        ],
        [
          'title' => 'Formation Personnalisée',
          'sku' => 'FORMATION-001',
          'price' => '300.00',
          'description' => 'Formation personnalisée de 4 heures',
        ],
      ];

      foreach ($test_products as $product_data) {
        // Créer la variation de produit
        $variation = \Drupal\commerce_product\Entity\ProductVariation::create([
          'type' => 'default',
          'sku' => $product_data['sku'],
          'title' => $product_data['title'],
          'price' => new \Drupal\commerce_price\Price($product_data['price'], 'EUR'),
          'status' => 1,
        ]);
        $variation->save();

        // Créer le produit
        $product = \Drupal\commerce_product\Entity\Product::create([
          'type' => 'default',
          'title' => $product_data['title'],
          'body' => [
            'value' => $product_data['description'],
            'format' => 'basic_html',
          ],
          'variations' => [$variation],
          'stores' => [\Drupal::service('commerce_store.current_store')->getStore()],
          'status' => 1,
        ]);
        $product->save();

        $products_created++;
      }

      \Drupal::messenger()->addStatus($products_created . ' produits de test créés avec succès !');
    } catch (\Exception $e) {
      \Drupal::messenger()->addError('Erreur lors de la création des produits: ' . $e->getMessage());
    }

    return $this->redirect('client_demandes.add_to_cart_test');
  }

  /**
   * Crée une commande de test pour le checkout.
   */
  public function createTestOrder()
  {
    try {
      // Créer une variation de produit de test
      $product_variation = \Drupal\commerce_product\Entity\ProductVariation::create([
        'type' => 'default',
        'sku' => 'TEST-' . time(),
        'title' => 'Service de test',
        'price' => new \Drupal\commerce_price\Price('99.99', 'EUR'),
        'status' => 1,
      ]);
      $product_variation->save();

      // Récupérer le store par défaut
      $stores = \Drupal::entityTypeManager()
        ->getStorage('commerce_store')
        ->loadMultiple();
      $store = reset($stores);

      // Récupérer l'utilisateur actuel
      $current_user = \Drupal::currentUser();
      $user = User::load($current_user->id());

      // Créer la commande
      $order = \Drupal\commerce_order\Entity\Order::create([
        'type' => 'default',
        'store_id' => $store->id(),
        'uid' => $user->id(),
        'mail' => $user->getEmail(),
        'ip_address' => \Drupal::request()->getClientIp(),
        'billing_profile' => NULL,
        'state' => 'draft',
        'placed' => \Drupal::time()->getRequestTime(),
      ]);

      $order->save();

      // Créer l'item de commande
      $order_item = \Drupal\commerce_order\Entity\OrderItem::create([
        'type' => 'default',
        'purchased_entity' => $product_variation->id(),
        'quantity' => 1,
        'unit_price' => $product_variation->getPrice(),
        'title' => 'Service de test - Checkout',
      ]);
      $order_item->save();

      // Ajouter l'item à la commande
      $order->addItem($order_item);
      $order->set('order_number', 'TEST-' . str_pad($order->id(), 6, '0', STR_PAD_LEFT));
      $order->recalculateTotalPrice();
      $order->save();

      \Drupal::messenger()->addStatus('Commande de test créée avec succès ! ID: ' . $order->id());

      // Rediriger vers le checkout de cette commande
      return $this->redirect('commerce_checkout.form', ['commerce_order' => $order->id()]);
    } catch (\Exception $e) {
      \Drupal::messenger()->addError('Erreur lors de la création de la commande de test: ' . $e->getMessage());
      return $this->redirect('client_demandes.checkout_test');
    }
  }

  /**
   * Créer les termes de taxonomie par défaut.
   */
  protected function createDefaultTaxonomyTerms()
  {
    $vocabulary_id = 'categories_service';
    $terms = [
      'Développement Web',
      'Design Graphique',
      'Marketing Digital',
      'Rédaction',
      'Traduction',
      'Photographie',
      'Vidéo',
      'Consulting',
      'Formation',
      'Maintenance Informatique',
    ];

    $term_storage = \Drupal::entityTypeManager()->getStorage('taxonomy_term');

    foreach ($terms as $term_name) {
      // Vérifier si le terme existe déjà
      $existing_terms = $term_storage->loadByProperties([
        'name' => $term_name,
        'vid' => $vocabulary_id,
      ]);

      if (empty($existing_terms)) {
        try {
          $term = $term_storage->create([
            'name' => $term_name,
            'vid' => $vocabulary_id,
          ]);
          $term->save();
        } catch (\Exception $e) {
          \Drupal::logger('client_demandes')->warning('Erreur lors de la création du terme @term: @error', [
            '@term' => $term_name,
            '@error' => $e->getMessage()
          ]);
        }
      }
    }
  }
}
