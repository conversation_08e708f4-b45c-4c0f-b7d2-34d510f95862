<?php

namespace Drupal\client_demandes\Plugin\views\field;

use Drupal\views\Plugin\views\field\FieldPluginBase;
use Drupal\views\ResultRow;

/**
 * Field handler to display store name for orders.
 *
 * @ViewsField("client_demandes_store_name")
 */
class StoreNameField extends FieldPluginBase {

  /**
   * {@inheritdoc}
   */
  public function query() {
    // Ne pas ajouter de requête, nous utilisons les données existantes.
  }

  /**
   * {@inheritdoc}
   */
  public function render(ResultRow $values) {
    $order = $this->getEntity($values);
    
    if ($order && method_exists($order, 'getStore')) {
      $store = $order->getStore();
      if ($store) {
        return [
          '#markup' => $store->getName(),
        ];
      }
    }
    
    return '';
  }

}
