<?php

namespace Drupal\client_demandes\Form;

use Drupal\Core\Form\FormBase;
use <PERSON>upal\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\node\Entity\Node;
use Drupal\Core\Session\AccountInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Drupal\Core\Entity\EntityTypeManagerInterface;
use Drupal\Core\Mail\MailManagerInterface;
use Drupal\Component\Utility\SafeMarkup;

/**
 * Formulaire pour publier une demande.
 */
class DemandeForm extends FormBase
{

  protected $currentUser;
  protected $entityTypeManager;

  public function __construct(AccountInterface $current_user, EntityTypeManagerInterface $entity_type_manager)
  {
    $this->currentUser = $current_user;
    $this->entityTypeManager = $entity_type_manager;
  }

  public static function create(ContainerInterface $container)
  {
    return new static(
      $container->get('current_user'),
      $container->get('entity_type.manager')
    );
  }

  public function getFormId()
  {
    return 'client_demandes_demande_form';
  }

  public function buildForm(array $form, FormStateInterface $form_state)
  {
    $categories = $this->getCategories();

    $form['title'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Titre de la demande'),
      '#required' => TRUE,
    ];

    $form['localisation'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Localisation'),
      '#required' => TRUE,
    ];

    $form['categorie'] = [
      '#type' => 'select',
      '#title' => $this->t('Catégorie'),
      '#options' => $categories,
      '#required' => TRUE,
    ];

    $form['body'] = [
      '#type' => 'text_format',
      '#title' => $this->t('Description'),
      '#format' => 'basic_html',
      '#required' => TRUE,
    ];

    $form['price'] = [
      '#type' => 'number',
      '#title' => $this->t('Budget (€)'),
      '#min' => 1,
      '#step' => 0.01,
      '#required' => TRUE,
    ];



    $form['actions']['submit'] = [
      '#type' => 'submit',
      '#value' => $this->t('Publier la demande'),
    ];

    return $form;
  }

  public function validateForm(array &$form, FormStateInterface $form_state)
  {
    if ($this->currentUser->isAnonymous() || !$this->currentUser->hasRole('client')) {
      $form_state->setErrorByName('title', $this->t('Vous devez être connecté comme client.'));
    }
  }

  public function submitForm(array &$form, FormStateInterface $form_state)
  {
    $demande = Node::create([
      'type' => 'demande',
      'title' => $form_state->getValue('title'),
      'uid' => $this->currentUser->id(),
      'status' => 1,
      'body' => $form_state->getValue('body'),
      'field_categorie' => $form_state->getValue('categorie'),
      'field_price' => $form_state->getValue('price'),
      'field_localisation' => $form_state->getValue('localisation'),
    ]);
    $demande->save();

    $this->notifyProviders($demande);

    $this->messenger()->addStatus($this->t('Demande publiée.'));
    $form_state->setRedirect('client_demandes.client_dashboard');
  }

  protected function getCategories()
  {
    try {
      $terms = $this->entityTypeManager->getStorage('taxonomy_term')->loadTree('categorie_metier');
      $categories = [];
      foreach ($terms as $term) {
        $categories[$term->tid] = $term->name;
      }
      return $categories;
    } catch (\Exception $e) {
      // Si le vocabulaire n'existe pas, retourner un tableau vide
      \Drupal::logger('client_demandes')->warning('Vocabulaire categorie_metier introuvable: @error', ['@error' => $e->getMessage()]);
      return [];
    }
  }

  protected function getProvidersByCategory($tid = NULL)
  {
    if (empty($tid)) {
      return [];
    }

    // Récupérer tous les prestataires actifs pour l'instant
    // TODO: Implémenter le filtrage par catégorie quand les champs utilisateur seront créés
    $uids = \Drupal::entityQuery('user')
      ->condition('status', 1)
      ->condition('roles', 'prestataire')
      ->accessCheck(TRUE)
      ->execute();

    $options = [];
    foreach (\Drupal\user\Entity\User::loadMultiple($uids) as $user) {
      $options[$user->id()] = $user->getDisplayName();
    }

    return $options;
  }

  protected function notifyProviders(Node $node)
  {
    $categorie_tid = $node->get('field_categorie')->target_id;
    $localisation = $node->get('field_localisation')->value;

    // Récupérer tous les prestataires actifs pour l'instant
    // TODO: Implémenter le filtrage par catégorie et localisation quand les champs utilisateur seront créés
    $uids = \Drupal::entityQuery('user')
      ->condition('status', 1)
      ->condition('roles', 'prestataire')
      ->accessCheck(TRUE)
      ->execute();

    foreach (\Drupal\user\Entity\User::loadMultiple($uids) as $provider) {
      $message = \Drupal\message\Entity\Message::create([
        'template' => 'demande_notification',
        'uid' => $provider->id(),
        'field_node_reference' => ['target_id' => $node->id()],
      ]);
      $message->save();

      $mailManager = \Drupal::service('plugin.manager.mail');
      $module = 'client_demandes';
      $key = 'demande_notification';
      $to = $provider->getEmail();

      $demande_url = $node->toUrl('canonical', ['absolute' => TRUE])->toString();
      //$quote_url = \Drupal::url('client_demandes.send_quote_form', ['node' => $node->id()], ['absolute' => TRUE]);
      $quote_url = \Drupal::service('url_generator')->generateFromRoute('client_demandes.send_quote_form', ['node' => $node->id()], ['absolute' => TRUE]);


      $params['subject'] = 'Nouvelle demande correspondant à votre profil';
      $params['message'] = "Bonjour " . $provider->getDisplayName() . ",\n\nUne nouvelle demande correspondant à votre activité vient d’être publiée.\n\nTitre : " . $node->label() .
        "\nLocalisation : " . $localisation .
        "\n\nVoir la demande : " . $demande_url .
        "\nFaire une proposition : " . $quote_url;

      $langcode = $provider->getPreferredLangcode();
      $send = true;

      $mailManager->mail($module, $key, $to, $langcode, $params, NULL, $send);
    }
  }
}
