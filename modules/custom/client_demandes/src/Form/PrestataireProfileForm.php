<?php

namespace Drupal\client_demandes\Form;

use <PERSON><PERSON>al\Core\Form\FormBase;
use <PERSON><PERSON><PERSON>\Core\Form\FormStateInterface;
use <PERSON><PERSON><PERSON>\Core\Entity\EntityTypeManagerInterface;
use <PERSON><PERSON>al\Core\Session\AccountInterface;
use <PERSON><PERSON>fony\Component\DependencyInjection\ContainerInterface;
use Drupal\profile\Entity\Profile;
use Drupal\Core\Url;

/**
 * Formulaire pour créer/modifier le profil prestataire.
 */
class PrestataireProfileForm extends FormBase
{

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected $entityTypeManager;

  /**
   * The current user.
   *
   * @var \Drupal\Core\Session\AccountInterface
   */
  protected $currentUser;

  /**
   * Constructs a new PrestataireProfileForm.
   */
  public function __construct(EntityTypeManagerInterface $entity_type_manager, AccountInterface $current_user)
  {
    $this->entityTypeManager = $entity_type_manager;
    $this->currentUser = $current_user;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container)
  {
    return new static(
      $container->get('entity_type.manager'),
      $container->get('current_user')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId()
  {
    return 'prestataire_profile_form';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state)
  {
    // Charger le profil existant s'il existe
    $profile = $this->loadUserProfile();

    $form['localisation'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Zone d\'intervention'),
      '#description' => $this->t('Indiquez votre zone géographique d\'intervention (ville, région, etc.)'),
      '#required' => TRUE,
      '#default_value' => $profile ? $profile->get('field_localisation')->value : '',
    ];

    $form['categorie'] = [
      '#type' => 'select',
      '#title' => $this->t('Catégories de services'),
      '#description' => $this->t('Sélectionnez les catégories de services que vous proposez'),
      '#options' => $this->getCategories(),
      '#multiple' => TRUE,
      '#required' => TRUE,
      '#default_value' => $profile ? $this->getProfileCategories($profile) : [],
    ];

    $form['actions'] = [
      '#type' => 'actions',
    ];

    $form['actions']['submit'] = [
      '#type' => 'submit',
      '#value' => $this->t('Enregistrer le profil'),
      '#button_type' => 'primary',
    ];

    return $form;
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state)
  {
    $profile = $this->loadUserProfile();

    if (!$profile) {
      // Créer un nouveau profil
      $profile = Profile::create([
        'type' => 'prestataire',
        'uid' => $this->currentUser->id(),
        'status' => TRUE,
      ]);
    }

    // Mettre à jour les champs
    $profile->set('field_localisation', $form_state->getValue('localisation'));
    $profile->set('field_categorie', $form_state->getValue('categorie'));
    $profile->save();

    $this->messenger()->addStatus($this->t('Votre profil prestataire a été enregistré avec succès.'));
    $form_state->setRedirect('client_demandes.prestataire_dashboard');
  }

  /**
   * Charge le profil prestataire de l'utilisateur actuel.
   */
  protected function loadUserProfile()
  {
    $profiles = $this->entityTypeManager
      ->getStorage('profile')
      ->loadByProperties([
        'type' => 'prestataire',
        'uid' => $this->currentUser->id(),
      ]);

    return $profiles ? reset($profiles) : NULL;
  }

  /**
   * Récupère les catégories disponibles.
   */
  protected function getCategories()
  {
    try {
      $terms = $this->entityTypeManager->getStorage('taxonomy_term')->loadTree('categorie_metier');
      $categories = [];
      foreach ($terms as $term) {
        $categories[$term->tid] = $term->name;
      }
      return $categories;
    } catch (\Exception $e) {
      \Drupal::logger('client_demandes')->warning('Vocabulaire categorie_metier introuvable: @error', ['@error' => $e->getMessage()]);
      return [];
    }
  }

  /**
   * Récupère les catégories du profil.
   */
  protected function getProfileCategories($profile)
  {
    $categories = [];
    if ($profile->hasField('field_categorie') && !$profile->get('field_categorie')->isEmpty()) {
      foreach ($profile->get('field_categorie') as $item) {
        if ($item->target_id) {
          $categories[] = $item->target_id;
        }
      }
    }
    return $categories;
  }
}
