<?php

namespace Drupal\client_demandes\Form;

use <PERSON>upal\Core\Form\FormBase;
use <PERSON>upal\Core\Form\FormStateInterface;
use <PERSON>upal\node\Entity\Node;
use Drupal\commerce_order\Entity\Order;
use Drupal\commerce_order\Entity\OrderItem;
use Drupal\commerce_price\Price;
use Drupal\Core\Session\AccountInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Drupal\Core\Entity\EntityTypeManagerInterface;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Drupal\message\Entity\Message;

/**
 * Formulaire pour proposer un devis sur une demande.
 */
class SendQuoteForm extends FormBase
{

  protected $currentUser;
  protected $entityTypeManager;

  public function __construct(AccountInterface $current_user, EntityTypeManagerInterface $entity_type_manager)
  {
    $this->currentUser = $current_user;
    $this->entityTypeManager = $entity_type_manager;
  }

  public static function create(ContainerInterface $container)
  {
    return new static(
      $container->get('current_user'),
      $container->get('entity_type.manager')
    );
  }

  public function getFormId()
  {
    return 'client_demandes_send_quote_form';
  }

  public function buildForm(array $form, FormStateInterface $form_state, Node $node = NULL)
  {
    if (!$node || $node->bundle() !== 'demande') {
      throw new AccessDeniedHttpException();
    }

    $form['quote_amount'] = [
      '#type' => 'number',
      '#title' => $this->t('Montant du devis (€)'),
      '#required' => TRUE,
      '#step' => 0.01,
    ];

    $form['quote_description'] = [
      '#type' => 'textarea',
      '#title' => $this->t('Description du devis'),
      '#required' => TRUE,
    ];

    $form['mode_paiement'] = [
      '#type' => 'radios',
      '#title' => $this->t('Mode de paiement souhaité'),
      '#options' => [
        'complet' => $this->t('Paiement complet au prestataire'),
        'multi' => $this->t('Paiement partagé (plateforme + prestataire)'),
      ],
      '#default_value' => 'complet',
      '#required' => TRUE,
    ];

    $form['node_id'] = [
      '#type' => 'hidden',
      '#value' => $node->id(),
    ];

    $form['submit'] = [
      '#type' => 'submit',
      '#value' => $this->t('Envoyer le devis'),
    ];

    return $form;
  }

  public function submitForm(array &$form, FormStateInterface $form_state)
  {
    $node_id = $form_state->getValue('node_id');
    $amount = $form_state->getValue('quote_amount');
    $description = $form_state->getValue('quote_description');
    $mode = $form_state->getValue('mode_paiement');
    $node = Node::load($node_id);
    $client_uid = $node->getOwnerId();

    $devis = Node::create([
      'type' => 'devis',
      'title' => 'Devis proposé',
      'field_price' => $amount,
      'field_description' => $description,
      'field_mode_paiement' => $mode,
      'field_demande_ref' => ['target_id' => $node_id],
      'field_status' => 'pending',
      'uid' => $this->currentUser->id(),
      'status' => 1,
    ]);
    $devis->save();

    // Vérifier que le devis a bien été sauvegardé et a un ID
    if (!$devis->id()) {
      \Drupal::logger('client_demandes')->error('Erreur: Le devis n\'a pas pu être sauvegardé correctement.');
      \Drupal::messenger()->addError($this->t('Erreur lors de la création du devis.'));
      return;
    }

    // Envoyer une notification via le module Message
    $this->sendDevisNotification($devis, $node, $amount);

    // Créer un item de commande
    $order_item = OrderItem::create([
      'type' => 'default',
      'purchased_entity' => NULL,
      'title' => 'Prestation : ' . $node->label(),
      'quantity' => 1,
      'unit_price' => new Price($amount, 'EUR'),
    ]);
    $order_item->save();

    // Créer la commande
    $order = Order::create([
      'type' => 'default',
      'uid' => $client_uid,
      'state' => 'draft',
      'order_items' => [$order_item],
      'field_devis_ref' => ['target_id' => $devis->id()],
      'field_mode_paiement' => $mode,
    ]);
    $order->save();

    \Drupal::messenger()->addMessage($this->t('Le devis a été envoyé et une commande a été créée.'));
  }

  /**
   * Envoie une notification de devis via le module Message.
   */
  protected function sendDevisNotification($devis, $demande, $amount)
  {
    try {
      // Vérifier que le devis a un ID
      if (!$devis->id()) {
        \Drupal::logger('client_demandes')->error('Impossible d\'envoyer la notification: le devis n\'a pas d\'ID.');
        return;
      }

      // Vérifier que la demande a un ID
      if (!$demande->id()) {
        \Drupal::logger('client_demandes')->error('Impossible d\'envoyer la notification: la demande n\'a pas d\'ID.');
        return;
      }

      // Récupérer le propriétaire de la demande (client)
      $client = $demande->getOwner();
      if (!$client || !$client->id()) {
        \Drupal::logger('client_demandes')->error('Impossible d\'envoyer la notification: client introuvable.');
        return;
      }

      // Récupérer le nom du prestataire
      $prestataire = $this->currentUser;
      $prestataire_name = $prestataire->getDisplayName();

      // Vérifier que le template de message existe
      $message_template = \Drupal::entityTypeManager()->getStorage('message_template')->load('devis_notification');
      if (!$message_template) {
        \Drupal::logger('client_demandes')->warning('Template de message devis_notification introuvable. Notification non envoyée.');
        return;
      }

      // Créer le message de notification
      $message = Message::create([
        'template' => 'devis_notification',
        'uid' => $client->id(),
        'field_devis_reference' => ['target_id' => $devis->id()],
        'field_prestataire_name' => $prestataire_name,
        'field_demande_title' => $demande->getTitle(),
        'field_devis_price' => number_format((float) $amount, 2),
      ]);
      $message->save();

      // Vérifier que le message a été sauvegardé
      if ($message->id()) {
        \Drupal::logger('client_demandes')->info('Notification de devis envoyée: Devis @devis_id pour la demande @demande_id au client @client_id (Message @message_id)', [
          '@devis_id' => $devis->id(),
          '@demande_id' => $demande->id(),
          '@client_id' => $client->id(),
          '@message_id' => $message->id(),
        ]);
      } else {
        \Drupal::logger('client_demandes')->error('Erreur: Le message de notification n\'a pas pu être sauvegardé.');
      }
    } catch (\Exception $e) {
      // Log l'erreur mais ne pas interrompre le processus
      \Drupal::logger('client_demandes')->error('Erreur lors de l\'envoi de la notification de devis: @error', [
        '@error' => $e->getMessage(),
        '@trace' => $e->getTraceAsString(),
      ]);
    }
  }
}
