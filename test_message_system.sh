#!/bin/bash

# Script pour tester le système de messages
# Usage: ./test_message_system.sh

echo "=== Test du système de messages ==="

# Configuration de la base de données
DB_NAME="drupal"
DB_USER="drupal"
DB_PASS="drupal"
DB_HOST="localhost"

# Fonction pour exécuter une requête SQL et afficher le résultat
test_sql() {
    local query="$1"
    local description="$2"
    echo "[$description]"
    echo "Requête: $query"
    mysql -h"$DB_HOST" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "$query"
    echo ""
}

echo "1. Vérification du template de message..."
test_sql "SELECT template, label, status FROM message_template WHERE template = 'devis_notification';" "Template devis_notification"

echo "2. Vérification des field storages..."
test_sql "SELECT field_name, type, entity_type FROM field_storage_config WHERE entity_type = 'message' AND field_name LIKE 'field_%';" "Storages des champs"

echo "3. Vérification des field configs..."
test_sql "SELECT field_name, bundle, required FROM field_config WHERE entity_type = 'message' AND bundle = 'devis_notification';" "Configs des champs"

echo "4. Vérification des tables des champs..."
test_sql "SHOW TABLES LIKE 'message__field_%';" "Tables des champs"

echo "5. Vérification des messages existants..."
test_sql "SELECT m.mid, m.template, m.uid, FROM_UNIXTIME(m.created) as date_creation FROM message m WHERE m.template = 'devis_notification' ORDER BY m.created DESC LIMIT 5;" "Messages récents"

echo "6. Détails des messages avec champs..."
test_sql "SELECT 
    m.mid,
    m.uid,
    FROM_UNIXTIME(m.created) as date_creation,
    mdr.field_devis_reference_target_id as devis_id,
    mpn.field_prestataire_name_value as prestataire,
    mdt.field_demande_title_value as demande,
    mdp.field_devis_price_value as prix
FROM message m
LEFT JOIN message__field_devis_reference mdr ON m.mid = mdr.entity_id
LEFT JOIN message__field_prestataire_name mpn ON m.mid = mpn.entity_id
LEFT JOIN message__field_demande_title mdt ON m.mid = mdt.entity_id
LEFT JOIN message__field_devis_price mdp ON m.mid = mdp.entity_id
WHERE m.template = 'devis_notification'
ORDER BY m.created DESC
LIMIT 3;" "Messages détaillés"

echo "7. Statistiques..."
test_sql "SELECT 
    'Templates' as type, COUNT(*) as count FROM message_template WHERE template = 'devis_notification'
UNION ALL
SELECT 
    'Champs' as type, COUNT(*) as count FROM field_config WHERE entity_type = 'message' AND bundle = 'devis_notification'
UNION ALL
SELECT 
    'Messages' as type, COUNT(*) as count FROM message WHERE template = 'devis_notification';" "Statistiques globales"

echo "8. Test de création d'un nouveau message..."
test_sql "INSERT INTO message (template, uid, created, changed, langcode) VALUES ('devis_notification', 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'fr');" "Création message test"

# Récupérer l'ID et ajouter les champs
test_sql "SET @new_message_id = LAST_INSERT_ID();
INSERT INTO message__field_devis_reference (bundle, deleted, entity_id, revision_id, langcode, delta, field_devis_reference_target_id) VALUES ('devis_notification', 0, @new_message_id, @new_message_id, 'fr', 0, 2);
INSERT INTO message__field_prestataire_name (bundle, deleted, entity_id, revision_id, langcode, delta, field_prestataire_name_value) VALUES ('devis_notification', 0, @new_message_id, @new_message_id, 'fr', 0, 'Nouveau Prestataire');
INSERT INTO message__field_demande_title (bundle, deleted, entity_id, revision_id, langcode, delta, field_demande_title_value) VALUES ('devis_notification', 0, @new_message_id, @new_message_id, 'fr', 0, 'Nouvelle Demande');
INSERT INTO message__field_devis_price (bundle, deleted, entity_id, revision_id, langcode, delta, field_devis_price_value) VALUES ('devis_notification', 0, @new_message_id, @new_message_id, 'fr', 0, '250.00');" "Ajout des champs"

echo "9. Vérification du nouveau message..."
test_sql "SELECT 
    m.mid,
    mpn.field_prestataire_name_value as prestataire,
    mdt.field_demande_title_value as demande,
    mdp.field_devis_price_value as prix
FROM message m
LEFT JOIN message__field_prestataire_name mpn ON m.mid = mpn.entity_id
LEFT JOIN message__field_demande_title mdt ON m.mid = mdt.entity_id
LEFT JOIN message__field_devis_price mdp ON m.mid = mdp.entity_id
WHERE m.template = 'devis_notification'
ORDER BY m.mid DESC
LIMIT 1;" "Nouveau message créé"

echo ""
echo "=== Test terminé ==="
echo "Si toutes les requêtes ont réussi, le système de messages fonctionne correctement."
