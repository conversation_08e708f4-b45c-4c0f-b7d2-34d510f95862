#!/bin/bash

# Script pour supprimer complètement le système de messages
# Usage: ./delete_message_system.sh

echo "=== Suppression du système de messages ==="

# Configuration de la base de données
DB_NAME="renovation1"
DB_USER="root"
DB_PASS="password"
DB_HOST="localhost"

# Fonction pour exécuter une requête SQL
execute_sql() {
    local query="$1"
    echo "Exécution: $query"
    mysql -h"$DB_HOST" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "$query"
    if [ $? -eq 0 ]; then
        echo "✓ Succès"
    else
        echo "✗ Erreur"
    fi
    echo ""
}

echo "1. Suppression des messages existants..."
execute_sql "DELETE FROM message WHERE template = 'devis_notification';"

echo "2. Suppression des données des champs..."
execute_sql "DELETE FROM message__field_devis_reference WHERE bundle = 'devis_notification';"
execute_sql "DELETE FROM message__field_prestataire_name WHERE bundle = 'devis_notification';"
execute_sql "DELETE FROM message__field_demande_title WHERE bundle = 'devis_notification';"
execute_sql "DELETE FROM message__field_devis_price WHERE bundle = 'devis_notification';"

echo "3. Suppression des tables des champs..."
execute_sql "DROP TABLE IF EXISTS message__field_devis_reference;"
execute_sql "DROP TABLE IF EXISTS message__field_prestataire_name;"
execute_sql "DROP TABLE IF EXISTS message__field_demande_title;"
execute_sql "DROP TABLE IF EXISTS message__field_devis_price;"

echo "4. Suppression des configurations de champs..."
execute_sql "DELETE FROM field_config WHERE entity_type = 'message' AND bundle = 'devis_notification';"

echo "5. Suppression des storages de champs..."
execute_sql "DELETE FROM field_storage_config WHERE entity_type = 'message' AND field_name IN ('field_devis_reference', 'field_prestataire_name', 'field_demande_title', 'field_devis_price');"

echo "6. Suppression du template de message..."
execute_sql "DELETE FROM message_template WHERE template = 'devis_notification';"

echo "7. Suppression des données du template..."
execute_sql "DELETE FROM message_template__text WHERE entity_id = 'devis_notification';"

echo "8. Vider le cache Drupal..."
if command -v drush &> /dev/null; then
    drush cr
    echo "✓ Cache vidé avec Drush"
else
    echo "⚠ Drush non trouvé, videz le cache manuellement"
fi

echo ""
echo "=== Suppression terminée ==="
echo "Le système de messages a été complètement supprimé."
