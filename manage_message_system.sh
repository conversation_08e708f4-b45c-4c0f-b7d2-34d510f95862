#!/bin/bash

# Script principal pour gérer le système de messages
# Usage: ./manage_message_system.sh [action]
# Actions: install, uninstall, test, reset

ACTION="$1"

if [ -z "$ACTION" ]; then
    echo "Usage: $0 [install|uninstall|test|reset]"
    echo ""
    echo "Actions disponibles:"
    echo "  install   - Créer le système de messages"
    echo "  uninstall - Supprimer le système de messages"
    echo "  test      - Tester le système de messages"
    echo "  reset     - Supprimer puis recréer le système"
    exit 1
fi

case "$ACTION" in
    "install")
        echo "=== Installation du système de messages ==="
        ./create_message_system.sh
        ;;
    "uninstall")
        echo "=== Désinstallation du système de messages ==="
        ./delete_message_system.sh
        ;;
    "test")
        echo "=== Test du système de messages ==="
        ./test_message_system.sh
        ;;
    "reset")
        echo "=== Reset du système de messages ==="
        echo "1. Suppression..."
        ./delete_message_system.sh
        echo ""
        echo "2. Recréation..."
        ./create_message_system.sh
        echo ""
        echo "3. Test..."
        ./test_message_system.sh
        ;;
    *)
        echo "Action inconnue: $ACTION"
        echo "Actions disponibles: install, uninstall, test, reset"
        exit 1
        ;;
esac

echo ""
echo "=== Opération terminée ==="
